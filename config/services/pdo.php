<?php

declare(strict_types=1);

/**
 * PDO Service Configuration for HDM Boot Protocol
 * 
 * Provides PDO database connection for EventStore and other components.
 * Uses SQLite for lightweight, secure, and performant storage.
 */

use DI\Container;
use ResponsiveSk\Slim4Paths\Paths;

return [
    // PDO Service Definition
    \PDO::class => function (\DI\Container $container): \PDO {
        $paths = $container->get(Paths::class);
        if (!$paths instanceof \ResponsiveSk\Slim4Paths\Paths) {
            throw new \RuntimeException('Paths service not properly configured');
        }
        
        // Use EventStore SQLite database
        $eventStorePath = $paths->storage('eventstore.db');
        $dsn = "sqlite:{$eventStorePath}";
        
        // Ensure storage directory exists
        $storageDir = dirname($eventStorePath);
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0700, true);
        }
        
        try {
            $pdo = new \PDO($dsn, null, null, [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
                \PDO::ATTR_TIMEOUT => 30,
            ]);
            
            // Optimize SQLite for performance
            $pdo->exec('PRAGMA journal_mode=WAL');
            $pdo->exec('PRAGMA synchronous=NORMAL');
            $pdo->exec('PRAGMA cache_size=10000');
            $pdo->exec('PRAGMA temp_store=MEMORY');
            
            return $pdo;
            
        } catch (\PDOException $e) {
            throw new \RuntimeException(
                "Failed to create PDO connection to EventStore database: " . $e->getMessage(),
                0,
                $e
            );
        }
    },
    
    // Alternative: PDO Factory for multiple databases
    'pdo.factory' => function (\DI\Container $container): \Closure {
        return function (string $database = 'eventstore') use ($container): \PDO {
            $paths = $container->get(Paths::class);
            if (!$paths instanceof \ResponsiveSk\Slim4Paths\Paths) {
                throw new \RuntimeException('Paths service not properly configured');
            }
            
            $dbPath = $paths->storage("{$database}.db");
            $dsn = "sqlite:{$dbPath}";
            
            // Ensure storage directory exists
            $storageDir = dirname($dbPath);
            if (!is_dir($storageDir)) {
                mkdir($storageDir, 0700, true);
            }
            
            try {
                $pdo = new \PDO($dsn, null, null, [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                    \PDO::ATTR_EMULATE_PREPARES => false,
                    \PDO::ATTR_TIMEOUT => 30,
                ]);
                
                // Optimize SQLite for performance
                $pdo->exec('PRAGMA journal_mode=WAL');
                $pdo->exec('PRAGMA synchronous=NORMAL');
                $pdo->exec('PRAGMA cache_size=10000');
                $pdo->exec('PRAGMA temp_store=MEMORY');
                
                return $pdo;
                
            } catch (\PDOException $e) {
                throw new \RuntimeException(
                    "Failed to create PDO connection to {$database} database: " . $e->getMessage(),
                    0,
                    $e
                );
            }
        };
    },
];