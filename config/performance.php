<?php

declare(strict_types=1);

/**
 * HDM Boot Protocol - Performance Configuration
 * Optimizes PHP performance for faster server response
 */

return [
    'opcache' => [
        'enable' => true,
        'memory_consumption' => 128,
        'max_accelerated_files' => 4000,
        'revalidate_freq' => 60,
        'fast_shutdown' => true,
    ],
    
    'database' => [
        'connection_pooling' => true,
        'query_cache' => true,
        'prepared_statements' => true,
    ],
    
    'response' => [
        'compression' => true,
        'etag' => true,
        'last_modified' => true,
    ],
    
    'preload' => [
        'critical_classes' => [
            'HdmBoot\SharedKernel\Services\PathsFactory',
            'HdmBoot\Modules\Core\Database\UserSqliteDatabaseManager',
            'HdmBoot\Modules\Core\Database\MarkSqliteDatabaseManager',
        ],
    ],
];