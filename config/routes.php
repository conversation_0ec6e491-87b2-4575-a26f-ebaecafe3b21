<?php

declare(strict_types=1);

use HdmBoot\Modules\Core\Security\Actions\Web\LoginPageAction;
use HdmBoot\Modules\Core\Security\Actions\Web\LoginSubmitAction;
use HdmBoot\Modules\Core\Security\Actions\Web\LogoutAction;
use HdmBoot\Modules\Core\Security\Infrastructure\Middleware\UserAuthenticationMiddleware;
use HdmBoot\Modules\Core\Session\Infrastructure\Middleware\SessionStartMiddleware;
use HdmBoot\Modules\Core\User\Actions\Web\ProfilePageAction;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Slim\App;

/*
 * Consolidated Application Routes.
 *
 * All routes in one file for simplified configuration.
 */
return function (App $app): void {
    // Load API routes
    (require __DIR__ . '/routes/api.php')($app);

    // ===== HOME ROUTES =====
    $app->get('/', function (ServerRequestInterface $request, ResponseInterface $response) {
        // Load the MEGA welcome page template
        $templatePath = __DIR__ . '/../templates/welcome.php';

        if (!file_exists($templatePath)) {
            throw new \RuntimeException("Welcome template not found: {$templatePath}");
        }

        // Start output buffering
        ob_start();

        try {
            // Include the welcome template
            include $templatePath;

            // Get rendered content
            $content = ob_get_contents();

            if ($content === false) {
                throw new \RuntimeException('Failed to render welcome template');
            }

            // Clean buffer and write to response
            ob_end_clean();
            $response->getBody()->write($content);

            return $response->withHeader('Content-Type', 'text/html');
        } catch (\Throwable $e) {
            ob_end_clean(); // Clean buffer on error
            throw $e;
        }
    })->setName('home');

    // ===== WEB ROUTES =====
    // Authentication routes (with session middleware)
    $app->get('/login', LoginPageAction::class)
        ->setName('login')
        ->add(SessionStartMiddleware::class);

    $app->post('/login', LoginSubmitAction::class)
        ->setName('login-submit')
        ->add(SessionStartMiddleware::class);

    $app->get('/logout', LogoutAction::class)
        ->setName('logout-get')
        ->add(SessionStartMiddleware::class);

    $app->post('/logout', LogoutAction::class)
        ->setName('logout')
        ->add(SessionStartMiddleware::class);

    // Protected routes (with session + authentication middleware)
    $app->get('/profile', ProfilePageAction::class)
        ->setName('profile')
        ->add(UserAuthenticationMiddleware::class)
        ->add(SessionStartMiddleware::class);

    // ===== BLOG ROUTES =====
    // Blog routes are now loaded from Blog module (src/Modules/Optional/Blog/routes.php)

    // ===== MARK ROUTES =====
    (require __DIR__ . '/../src/Modules/Core/Mark/routes.php')($app);

    // ===== MONITORING ROUTES =====
    (require __DIR__ . '/routes/monitoring.php')($app);

    // ===== DOCUMENTATION ROUTES =====
    (require __DIR__ . '/routes/docs.php')($app);
};
