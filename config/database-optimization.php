<?php

declare(strict_types=1);

/**
 * HDM Boot Protocol - Database Performance Optimizer
 */

return [
    'sqlite_optimizations' => [
        'PRAGMA journal_mode = WAL',
        'PRAGMA synchronous = NORMAL',
        'PRAGMA cache_size = 10000',
        'PRAGMA temp_store = MEMORY',
        'PRAGMA mmap_size = 268435456', // 256MB
    ],
    
    'connection_settings' => [
        'persistent' => true,
        'timeout' => 30,
        'retry_attempts' => 3,
    ],
    
    'query_optimizations' => [
        'use_indexes' => true,
        'limit_results' => true,
        'cache_frequent_queries' => true,
    ],
];