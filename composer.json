{"name": "responsive-sk/slim4-paths", "version": "2.2.0", "description": "Enhanced secure path management for PHP applications with 30+ predefined paths, security validation, and Orbit CMS support", "type": "library", "license": "MIT", "keywords": ["slim", "slim4", "paths", "php", "framework", "security", "orbit", "cms", "path-traversal", "validation"], "authors": [{"name": "Responsive SK", "email": "<EMAIL>"}], "require": {"php": "^8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "phpstan/phpstan": "^1.10", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"ResponsiveSk\\Slim4Paths\\": "src/"}}, "autoload-dev": {"psr-4": {"ResponsiveSk\\Slim4Paths\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "phpstan": "phpstan analyse src --level=max", "cs": "phpcs --standard=PSR12 src", "cs-fix": "phpcbf --standard=PSR12 src"}, "minimum-stability": "stable", "prefer-stable": true}