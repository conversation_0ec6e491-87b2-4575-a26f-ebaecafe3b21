<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HDM Boot Protocol - Welcome</title>
    <meta name="description" content="HDM Boot Protocol - Modern PHP framework with Hexagonal-DDD-MMA architecture">
    
    <!-- HDM Boot Optimized Theme Assets -->
    <!-- Critical CSS first -->
    <link href="/themes/default/css/critical.min.css" rel="stylesheet">

    <!-- Main CSS (async load) -->
    <link rel="preload" href="/themes/default/css/styles.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="/themes/default/css/styles.min.css"></noscript>

    <!-- JavaScript (defer for performance) -->
    <script src="/themes/default/js/app.min.js" defer></script>
    
    <!-- Custom Welcome Styles -->
    <style>
        /* Pre-loader Styles */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .preloader.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .loader {
            width: 80px;
            height: 80px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loader-text {
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            margin-top: 2rem;
            animation: pulse 2s ease-in-out infinite;
        }
        
        /* Hero Animations */
        .hero-bg {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e40af 100%);
            position: relative;
            overflow: hidden;
        }
        
        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .feature-card {
            transition: all 0.3s ease;
            transform: translateY(0);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .tech-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(37, 99, 235, 0.1);
            border: 1px solid rgba(37, 99, 235, 0.2);
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            color: #2563eb;
            margin: 0.25rem;
            transition: all 0.3s ease;
        }
        
        .tech-badge:hover {
            background: rgba(37, 99, 235, 0.2);
            transform: scale(1.05);
        }
        
        .stats-counter {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Scroll Indicator */
        .scroll-indicator {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
            40% { transform: translateX(-50%) translateY(-10px); }
            60% { transform: translateX(-50%) translateY(-5px); }
        }
    </style>
</head>
<body class="bg-secondary-50 dark:bg-secondary-900" x-data="{ darkMode: false }" :class="{ 'dark': darkMode }">
    
    <!-- Pre-loader -->
    <div id="preloader" class="preloader">
        <div class="text-center">
            <div class="loader"></div>
            <div class="loader-text">Loading HDM Boot Protocol...</div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-bg min-h-screen flex items-center justify-center relative text-white">
        <div class="container mx-auto px-4 text-center relative z-10">
            <!-- Main Hero Content -->
            <div class="animate-fade-in">
                <h1 class="text-6xl md:text-8xl font-black mb-6 text-shadow-lg">
                    🚀 HDM Boot
                </h1>
                <h2 class="text-2xl md:text-4xl font-light mb-8 opacity-90">
                    Protocol Framework
                </h2>
                <p class="text-xl md:text-2xl mb-12 max-w-3xl mx-auto leading-relaxed opacity-80">
                    Modern PHP framework with <strong>Hexagonal-DDD-MMA</strong> architecture.<br>
                    Built for enterprise applications with clean code principles.
                </p>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center mb-16 animate-on-scroll">
                <a href="/blog" class="btn bg-white text-primary-600 hover:bg-secondary-100 px-8 py-4 text-lg font-semibold rounded-lg shadow-lg transform hover:scale-105 transition-all duration-300">
                    📚 Explore Blog
                </a>
                <a href="/profile" class="btn bg-primary-700 hover:bg-primary-800 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg transform hover:scale-105 transition-all duration-300">
                    👤 User Profile
                </a>
                <a href="/mark" class="btn bg-secondary-600 hover:bg-secondary-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg transform hover:scale-105 transition-all duration-300">
                    ⚡ Mark System
                </a>
            </div>
            
            <!-- Tech Stack -->
            <div class="animate-on-scroll">
                <h3 class="text-lg font-semibold mb-6 opacity-90">Powered by Modern Technologies</h3>
                <div class="flex flex-wrap justify-center gap-3 mb-12">
                    <span class="tech-badge bg-white/10 text-white border-white/20">PHP 8.3+</span>
                    <span class="tech-badge bg-white/10 text-white border-white/20">Slim Framework 4</span>
                    <span class="tech-badge bg-white/10 text-white border-white/20">TailwindCSS</span>
                    <span class="tech-badge bg-white/10 text-white border-white/20">AlpineJS</span>
                    <span class="tech-badge bg-white/10 text-white border-white/20">GSAP</span>
                    <span class="tech-badge bg-white/10 text-white border-white/20">SQLite</span>
                    <span class="tech-badge bg-white/10 text-white border-white/20">JWT Auth</span>
                    <span class="tech-badge bg-white/10 text-white border-white/20">DDD Architecture</span>
                </div>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="scroll-indicator">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white dark:bg-secondary-800">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-4xl md:text-5xl font-bold text-secondary-900 dark:text-secondary-100 mb-6">
                    Why HDM Boot Protocol?
                </h2>
                <p class="text-xl text-secondary-600 dark:text-secondary-400 max-w-3xl mx-auto">
                    Built with enterprise-grade architecture patterns and modern development practices
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="feature-card bg-white dark:bg-secondary-700 p-8 rounded-xl shadow-soft border border-secondary-200 dark:border-secondary-600 animate-on-scroll">
                    <div class="text-4xl mb-4">🏗️</div>
                    <h3 class="text-2xl font-bold text-secondary-900 dark:text-secondary-100 mb-4">HDM Architecture</h3>
                    <p class="text-secondary-600 dark:text-secondary-400 mb-6">
                        Hexagonal-DDD-MMA architecture ensures clean separation of concerns and maintainable code.
                    </p>
                    <ul class="text-sm text-secondary-500 dark:text-secondary-400 space-y-2">
                        <li>✅ Domain-Driven Design</li>
                        <li>✅ Hexagonal Architecture</li>
                        <li>✅ Modular Monolith</li>
                    </ul>
                </div>
                
                <!-- Feature 2 -->
                <div class="feature-card bg-white dark:bg-secondary-700 p-8 rounded-xl shadow-soft border border-secondary-200 dark:border-secondary-600 animate-on-scroll">
                    <div class="text-4xl mb-4">🔒</div>
                    <h3 class="text-2xl font-bold text-secondary-900 dark:text-secondary-100 mb-4">Enterprise Security</h3>
                    <p class="text-secondary-600 dark:text-secondary-400 mb-6">
                        Built-in security features with JWT authentication, CSRF protection, and secure file permissions.
                    </p>
                    <ul class="text-sm text-secondary-500 dark:text-secondary-400 space-y-2">
                        <li>✅ JWT Authentication</li>
                        <li>✅ CSRF Protection</li>
                        <li>✅ Secure File System</li>
                    </ul>
                </div>
                
                <!-- Feature 3 -->
                <div class="feature-card bg-white dark:bg-secondary-700 p-8 rounded-xl shadow-soft border border-secondary-200 dark:border-secondary-600 animate-on-scroll">
                    <div class="text-4xl mb-4">⚡</div>
                    <h3 class="text-2xl font-bold text-secondary-900 dark:text-secondary-100 mb-4">Modern Stack</h3>
                    <p class="text-secondary-600 dark:text-secondary-400 mb-6">
                        Latest PHP 8.3+ features with modern frontend technologies and optimized performance.
                    </p>
                    <ul class="text-sm text-secondary-500 dark:text-secondary-400 space-y-2">
                        <li>✅ PHP 8.3+ Features</li>
                        <li>✅ Modern Frontend</li>
                        <li>✅ Optimized Performance</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-20 bg-gradient-primary text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold mb-16 animate-on-scroll">Framework Statistics</h2>
            <div class="grid md:grid-cols-4 gap-8">
                <div class="animate-on-scroll">
                    <div class="stats-counter stat-number text-white" data-target="100">0</div>
                    <div class="text-lg opacity-90">% Type Safe</div>
                </div>
                <div class="animate-on-scroll">
                    <div class="stats-counter stat-number text-white" data-target="3">0</div>
                    <div class="text-lg opacity-90">Database System</div>
                </div>
                <div class="animate-on-scroll">
                    <div class="stats-counter stat-number text-white" data-target="15">0</div>
                    <div class="text-lg opacity-90">Core Modules</div>
                </div>
                <div class="animate-on-scroll">
                    <div class="stats-counter stat-number text-white" data-target="99">0</div>
                    <div class="text-lg opacity-90">% Test Coverage</div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-white dark:bg-secondary-800">
        <div class="container mx-auto px-4 text-center">
            <div class="max-w-3xl mx-auto animate-on-scroll">
                <h2 class="text-4xl md:text-5xl font-bold text-secondary-900 dark:text-secondary-100 mb-6">
                    Ready to Start Building?
                </h2>
                <p class="text-xl text-secondary-600 dark:text-secondary-400 mb-12">
                    Join the HDM Boot Protocol community and build enterprise-grade applications with modern PHP.
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="/blog" class="btn btn-primary px-8 py-4 text-lg font-semibold transform hover:scale-105 transition-all duration-300">
                        📖 Read Documentation
                    </a>
                    <a href="https://github.com/hdm-boot" class="btn btn-outline px-8 py-4 text-lg font-semibold transform hover:scale-105 transition-all duration-300">
                        ⭐ Star on GitHub
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Dark Mode Toggle (Floating) -->
    <button 
        @click="darkMode = !darkMode; document.documentElement.classList.toggle('dark')"
        class="fixed bottom-6 right-6 p-4 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg transform hover:scale-110 transition-all duration-300 z-50"
    >
        <svg x-show="!darkMode" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
        </svg>
        <svg x-show="darkMode" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
        </svg>
    </button>

    <!-- Pre-loader Script -->
    <script>
        // Pre-loader functionality
        window.addEventListener('load', function() {
            setTimeout(function() {
                const preloader = document.getElementById('preloader');
                preloader.classList.add('hidden');
                
                // Remove preloader from DOM after transition
                setTimeout(function() {
                    preloader.remove();
                }, 500);
            }, 1500); // Show preloader for 1.5 seconds
        });
        
        // Enhanced stats counter for welcome page
        document.addEventListener('DOMContentLoaded', function() {
            // Override the default stat counter for welcome page
            const statNumbers = document.querySelectorAll('.stats-counter[data-target]');
            
            statNumbers.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const obj = { value: 0 };
                
                // Use GSAP if available, otherwise fallback
                if (window.gsap) {
                    gsap.to(obj, {
                        duration: 2.5,
                        value: target,
                        ease: 'power2.out',
                        onUpdate: () => {
                            counter.textContent = Math.round(obj.value);
                        },
                        scrollTrigger: {
                            trigger: counter,
                            start: 'top 80%',
                            toggleActions: 'play none none none'
                        }
                    });
                } else {
                    // Fallback animation
                    let current = 0;
                    const increment = target / 100;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        counter.textContent = Math.round(current);
                    }, 25);
                }
            });
        });
    </script>
</body>
</html>
