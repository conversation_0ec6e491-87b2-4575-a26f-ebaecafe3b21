{"version": 1, "defects": {"MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetConnection": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testTestConnection": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetStatistics": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testExecuteRawSql": 8, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testExecuteRawSqlWithInvalidSql": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testDatabaseExists": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetDatabasePath": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testDatabaseInitialization": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testSqliteOptimizations": 7, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testMultipleConnections": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testTransactionSupport": 5, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testTransactionRollback": 5, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithValidPath": 5, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithPathTraversal": 5, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithInvalidDirectory": 7, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithEmptyPath": 7, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithAbsolutePath": 7, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testFileExists": 5, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFile": 8, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFileWithInvalidPath": 7, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testGetAllowedDirectories": 7, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testValidatePathSecurity": 8, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testNormalizePath": 8, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithDifferentDirectories": 8, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithSubdirectories": 5, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithSpecialCharacters": 7, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testPathSecurityWithNullBytes": 5, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFileCreatesDirectory": 8, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathPerformance": 5, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseConnection": 5, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseHasTestUser": 5, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseMetadata": 5, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testBasicQuery": 5, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testTableExists": 5, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testSuccessfulLogin": 7, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithInvalidCredentials": 5, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithNonExistentUser": 5, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithMissingEmail": 5, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithMissingPassword": 5, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithInvalidEmailFormat": 5, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithValidToken": 7, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithoutToken": 7, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithInvalidToken": 7, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testRefreshTokenWithValidToken": 1, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testRefreshTokenWithoutToken": 1, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLogoutWithValidToken": 7, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLogoutWithoutToken": 7, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testTokenExpiration": 5, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testMultipleLoginAttempts": 7, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testConcurrentTokenUsage": 7, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testLoginEndpoint": 7, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testMeEndpoint": 7, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testUsersEndpoint": 7, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testUnauthorizedAccess": 5, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testInvalidLogin": 7, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testListUsersWithAuthentication": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testListUsersWithoutAuthentication": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testListUsersWithPagination": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testListUsersWithFilters": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testGetUserById": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testGetUserByIdNotFound": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testGetUserWithoutAuthentication": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testCreateUser": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithInvalidData": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithDuplicateEmail": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithoutAuthentication": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testUpdateUserNotImplemented": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testDeleteUserNotImplemented": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testUserApiRequiresProperPermissions": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testUserDataStructure": 5, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testUserSearchFunctionality": 5, "MvaBootstrap\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareStartsSessionIfNotStarted": 8, "MvaBootstrap\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareAllowsAccessForAuthenticatedUser": 8, "MvaBootstrap\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareRedirectsUnauthenticatedUser": 8, "MvaBootstrap\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareRedirectsInactiveUser": 8, "MvaBootstrap\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareHandlesUserServiceException": 8, "MvaBootstrap\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareReturnsJsonForApiRequests": 8, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::completeAuthenticationFlowWorks": 8, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::loginPageLoadsCorrectly": 8, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::loginWithValidCredentialsWorks": 5, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::loginWithInvalidCredentialsFails": 5, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::profilePageRequiresAuthentication": 8, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::authenticatedUserCanAccessProfile": 5, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::sessionPersistsAcrossRequests": 5, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::logoutClearsSession": 5, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::csrfProtectionWorks": 8, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::enterpriseLoggingWorks": 5, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceCanGetUserById": 8, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceCanGetUserByEmail": 8, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceCanAuthenticateUser": 8, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForInactiveUser": 8, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceHasPermissionChecks": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleIsLoaded": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleHasManifest": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testSecurityServicesAreRegistered": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testCsrfServiceCanBeCreated": 7, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testJwtServiceCanBeCreated": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testAuthenticationServiceCanBeCreated": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testAuthorizationServiceCanBeCreated": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testJwtServiceConfiguration": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleManifestMetadata": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleDependencies": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testModuleIsLoaded": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testModuleHasManifest": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testUserServicesAreRegistered": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanBeCreated": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testUserRepositoryCanBeCreated": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanCreateUser": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanFindUserByEmail": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanAuthenticateUser": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testModuleManifestMetadata": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\UserModuleTest::testModuleDependencies": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleIsLoaded": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleHasCorrectDependencies": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServiceIsRegistered": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testCsrfServiceIsRegistered": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionInterfaceIsRegistered": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServiceCanBeCreated": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testCsrfServiceCanBeCreated": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleHasCorrectMetadata": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleConfigurationIsValid": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleStatusInformation": 8, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleHasManifest": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServicesAreRegistered": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleManifestMetadata": 5, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleDependencies": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetConnection": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testTestConnection": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetStatistics": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testExecuteRawSql": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testExecuteRawSqlWithInvalidSql": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testDatabaseExists": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetDatabasePath": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testDatabaseInitialization": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testSqliteOptimizations": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testMultipleConnections": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testTransactionSupport": 5, "HdmBoot\\Tests\\Unit\\Database\\DatabaseManagerTest::testTransactionRollback": 5, "HdmBoot\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseConnection": 5, "HdmBoot\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseHasTestUser": 5, "HdmBoot\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseMetadata": 5, "HdmBoot\\Tests\\Unit\\Database\\SimpleDatabaseTest::testBasicQuery": 5, "HdmBoot\\Tests\\Unit\\Database\\SimpleDatabaseTest::testTableExists": 5, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCreatesCorrectRepositoryType": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsCorrectRepositoryType": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsCorrectDatabaseManager": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsSupportedTypes": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryChecksIfTypeIsSupported": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryThrowsExceptionForUnsupportedType": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryRequiresPdoForSqliteRepository": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryThrowsExceptionForUnimplementedTypes": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCanBeConfiguredWithDifferentTypes": 8, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCreatesRepositoryWithProperAbstraction": 8, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithValidPath": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithPathTraversal": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithInvalidDirectory": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithEmptyPath": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithAbsolutePath": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testFileExists": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFile": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFileWithInvalidPath": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testGetAllowedDirectories": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testValidatePathSecurity": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testNormalizePath": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithDifferentDirectories": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithSubdirectories": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithSpecialCharacters": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testPathSecurityWithNullBytes": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFileCreatesDirectory": 5, "HdmBoot\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathPerformance": 5, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareStartsSessionIfNotStarted": 8, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareAllowsAccessForAuthenticatedUser": 8, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareRedirectsUnauthenticatedUser": 8, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareRedirectsInactiveUser": 8, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareHandlesUserServiceException": 8, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareReturnsJsonForApiRequests": 7, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleIsLoaded": 5, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleHasManifest": 5, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testSecurityServicesAreRegistered": 5, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testJwtServiceCanBeCreated": 5, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testAuthenticationServiceCanBeCreated": 5, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testAuthorizationServiceCanBeCreated": 5, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testJwtServiceConfiguration": 5, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleManifestMetadata": 5, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleDependencies": 5, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleIsLoaded": 5, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleHasManifest": 5, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServicesAreRegistered": 5, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServiceCanBeCreated": 5, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testCsrfServiceCanBeCreated": 5, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleManifestMetadata": 5, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleDependencies": 5, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testModuleIsLoaded": 5, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testModuleHasManifest": 7, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServicesAreRegistered": 5, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanBeCreated": 8, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserRepositoryCanBeCreated": 8, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanCreateUser": 8, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanFindUserByEmail": 8, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanAuthenticateUser": 8, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testModuleManifestMetadata": 8, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testModuleDependencies": 8, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceCanCreateUser": 5, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForDuplicateEmail": 7, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceCanGetUserById": 8, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceReturnsNullForInvalidUserId": 7, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceCanGetUserByEmail": 8, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceCanAuthenticateUser": 8, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForInvalidCredentials": 7, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForInactiveUser": 8, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesUserInput": 7, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesEmailFormat": 7, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesPasswordStrength": 7, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceHasPermissionChecks": 8, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::completeAuthenticationFlowWorks": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::loginPageLoadsCorrectly": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::loginWithValidCredentialsWorks": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::loginWithInvalidCredentialsFails": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::profilePageRequiresAuthentication": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::authenticatedUserCanAccessProfile": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::sessionPersistsAcrossRequests": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::logoutClearsSession": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::csrfProtectionWorks": 5, "HdmBoot\\Tests\\Integration\\AuthenticationFlowTest::enterpriseLoggingWorks": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testSuccessfulLogin": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithInvalidCredentials": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithNonExistentUser": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithMissingEmail": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithMissingPassword": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithInvalidEmailFormat": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithValidToken": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithoutToken": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithInvalidToken": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testRefreshTokenWithValidToken": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testRefreshTokenWithoutToken": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testLogoutWithValidToken": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testLogoutWithoutToken": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testTokenExpiration": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testMultipleLoginAttempts": 5, "HdmBoot\\Tests\\Feature\\Security\\AuthenticationTest::testConcurrentTokenUsage": 5, "HdmBoot\\Tests\\Feature\\SimpleApiTest::testLoginEndpoint": 5, "HdmBoot\\Tests\\Feature\\SimpleApiTest::testMeEndpoint": 5, "HdmBoot\\Tests\\Feature\\SimpleApiTest::testUsersEndpoint": 5, "HdmBoot\\Tests\\Feature\\SimpleApiTest::testUnauthorizedAccess": 5, "HdmBoot\\Tests\\Feature\\SimpleApiTest::testInvalidLogin": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testListUsersWithAuthentication": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testListUsersWithoutAuthentication": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testListUsersWithPagination": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testListUsersWithFilters": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testGetUserById": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testGetUserByIdNotFound": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testGetUserWithoutAuthentication": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testCreateUser": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithInvalidData": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithDuplicateEmail": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithoutAuthentication": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testUpdateUserNotImplemented": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testDeleteUserNotImplemented": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testUserApiRequiresProperPermissions": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testUserDataStructure": 5, "HdmBoot\\Tests\\Feature\\User\\UserApiTest::testUserSearchFunctionality": 5}, "times": {"MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetConnection": 0, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testTestConnection": 0.001, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetStatistics": 0.002, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testExecuteRawSql": 0.004, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testExecuteRawSqlWithInvalidSql": 0.001, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testDatabaseExists": 0.001, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testGetDatabasePath": 0.001, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testDatabaseInitialization": 0, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testSqliteOptimizations": 0.001, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testMultipleConnections": 0, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testTransactionSupport": 0, "MvaBootstrap\\Tests\\Unit\\Database\\DatabaseManagerTest::testTransactionRollback": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithValidPath": 0, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithPathTraversal": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithInvalidDirectory": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithEmptyPath": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithAbsolutePath": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testFileExists": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFile": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFileWithInvalidPath": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testGetAllowedDirectories": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testValidatePathSecurity": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testNormalizePath": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithDifferentDirectories": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithSubdirectories": 0, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathWithSpecialCharacters": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testPathSecurityWithNullBytes": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testCreateSecureFileCreatesDirectory": 0.001, "MvaBootstrap\\Tests\\Unit\\Helpers\\SecurePathHelperTest::testSecurePathPerformance": 0.008, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseConnection": 0, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseHasTestUser": 0, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testDatabaseMetadata": 0, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testBasicQuery": 0, "MvaBootstrap\\Tests\\Unit\\Database\\SimpleDatabaseTest::testTableExists": 0, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testSuccessfulLogin": 0.173, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithInvalidCredentials": 0.165, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithNonExistentUser": 0.002, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithMissingEmail": 0.001, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithMissingPassword": 0.001, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLoginWithInvalidEmailFormat": 0.001, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithValidToken": 0.166, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithoutToken": 0.008, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testMeEndpointWithInvalidToken": 0.003, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testRefreshTokenWithValidToken": 0.001, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testRefreshTokenWithoutToken": 0.001, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLogoutWithValidToken": 0.167, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testLogoutWithoutToken": 0.001, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testTokenExpiration": 0.165, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testMultipleLoginAttempts": 0.492, "MvaBootstrap\\Tests\\Feature\\Security\\AuthenticationTest::testConcurrentTokenUsage": 0.168, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testLoginEndpoint": 0.165, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testMeEndpoint": 0.166, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testUsersEndpoint": 0.167, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testUnauthorizedAccess": 0.001, "MvaBootstrap\\Tests\\Feature\\SimpleApiTest::testInvalidLogin": 0.165, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testListUsersWithAuthentication": 0.167, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testListUsersWithoutAuthentication": 0.001, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testListUsersWithPagination": 0.166, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testListUsersWithFilters": 0.166, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testGetUserById": 0.166, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testGetUserByIdNotFound": 0.166, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testGetUserWithoutAuthentication": 0.002, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testCreateUser": 0.324, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithInvalidData": 0.166, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithDuplicateEmail": 0.185, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testCreateUserWithoutAuthentication": 0.001, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testUpdateUserNotImplemented": 0.165, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testDeleteUserNotImplemented": 0.165, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testUserApiRequiresProperPermissions": 0.166, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testUserDataStructure": 0.172, "MvaBootstrap\\Tests\\Feature\\User\\UserApiTest::testUserSearchFunctionality": 0.17, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCreatesCorrectRepositoryType": 0.003, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsCorrectRepositoryType": 0.001, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsCorrectDatabaseManager": 0, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsSupportedTypes": 0.001, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryChecksIfTypeIsSupported": 0.001, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryThrowsExceptionForUnsupportedType": 0.001, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryRequiresPdoForSqliteRepository": 0.001, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryThrowsExceptionForUnimplementedTypes": 0.004, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCanBeConfiguredWithDifferentTypes": 0, "MvaBootstrap\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCreatesRepositoryWithProperAbstraction": 0.001, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::completeAuthenticationFlowWorks": 0.005, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::loginPageLoadsCorrectly": 0.005, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::loginWithValidCredentialsWorks": 0.005, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::loginWithInvalidCredentialsFails": 0.005, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::profilePageRequiresAuthentication": 0.001, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::authenticatedUserCanAccessProfile": 0.007, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::sessionPersistsAcrossRequests": 0.005, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::logoutClearsSession": 0.006, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::csrfProtectionWorks": 0.001, "MvaBootstrap\\Tests\\Integration\\AuthenticationFlowTest::enterpriseLoggingWorks": 0.005, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceCanCreateUser": 0.282, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForDuplicateEmail": 0.001, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceCanGetUserById": 0.002, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceReturnsNullForInvalidUserId": 0, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceCanGetUserByEmail": 0, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceCanAuthenticateUser": 0, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForInvalidCredentials": 0, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForInactiveUser": 0, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesUserInput": 0, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesEmailFormat": 0, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesPasswordStrength": 0, "MvaBootstrap\\Tests\\Unit\\Services\\UserServiceTest::serviceHasPermissionChecks": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleIsLoaded": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleHasManifest": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testSecurityServicesAreRegistered": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testCsrfServiceCanBeCreated": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testJwtServiceCanBeCreated": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testAuthenticationServiceCanBeCreated": 0.169, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testAuthorizationServiceCanBeCreated": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testJwtServiceConfiguration": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleManifestMetadata": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleDependencies": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleIsLoaded": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleHasCorrectDependencies": 0.002, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServiceIsRegistered": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testCsrfServiceIsRegistered": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionInterfaceIsRegistered": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServiceCanBeCreated": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testCsrfServiceCanBeCreated": 0.001, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleHasCorrectMetadata": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleConfigurationIsValid": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleStatusInformation": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleHasManifest": 0.002, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServicesAreRegistered": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleManifestMetadata": 0, "MvaBootstrap\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleDependencies": 0, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleIsLoaded": 0, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleHasManifest": 0, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testSecurityServicesAreRegistered": 0, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testJwtServiceCanBeCreated": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testAuthenticationServiceCanBeCreated": 0.194, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testAuthorizationServiceCanBeCreated": 0, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testJwtServiceConfiguration": 0, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleManifestMetadata": 0, "HdmBoot\\Tests\\Unit\\Modules\\SecurityModuleTest::testModuleDependencies": 0, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleIsLoaded": 0, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleHasManifest": 0, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServicesAreRegistered": 0, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testSessionServiceCanBeCreated": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testCsrfServiceCanBeCreated": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleManifestMetadata": 0, "HdmBoot\\Tests\\Unit\\Modules\\SessionModuleTest::testModuleDependencies": 0, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testModuleIsLoaded": 0, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testModuleHasManifest": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServicesAreRegistered": 0, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanBeCreated": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserRepositoryCanBeCreated": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanCreateUser": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanFindUserByEmail": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testUserServiceCanAuthenticateUser": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testModuleManifestMetadata": 0.001, "HdmBoot\\Tests\\Unit\\Modules\\UserModuleTest::testModuleDependencies": 0, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCreatesCorrectRepositoryType": 0.166, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsCorrectRepositoryType": 0.002, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsCorrectDatabaseManager": 0, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryReturnsSupportedTypes": 0.001, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryChecksIfTypeIsSupported": 0, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryThrowsExceptionForUnsupportedType": 0, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryRequiresPdoForSqliteRepository": 0.001, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryThrowsExceptionForUnimplementedTypes": 0.003, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCanBeConfiguredWithDifferentTypes": 0, "HdmBoot\\Tests\\Unit\\Factories\\RepositoryFactoryTest::factoryCreatesRepositoryWithProperAbstraction": 0.166, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceCanCreateUser": 0.165, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForDuplicateEmail": 0.001, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceCanGetUserById": 0.004, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceReturnsNullForInvalidUserId": 0.001, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceCanGetUserByEmail": 0.002, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceCanAuthenticateUser": 0, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForInvalidCredentials": 0.001, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceThrowsExceptionForInactiveUser": 0.001, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesUserInput": 0.001, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesEmailFormat": 0.001, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceValidatesPasswordStrength": 0.167, "HdmBoot\\Tests\\Unit\\Services\\UserServiceTest::serviceHasPermissionChecks": 0.001, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareStartsSessionIfNotStarted": 0.007, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareAllowsAccessForAuthenticatedUser": 0.002, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareRedirectsUnauthenticatedUser": 0.001, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareRedirectsInactiveUser": 0.001, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareHandlesUserServiceException": 0.001, "HdmBoot\\Tests\\Unit\\Middleware\\UserAuthenticationMiddlewareTest::middlewareReturnsJsonForApiRequests": 0.002}}