# HDM Boot Protocol - FTPS Deployment Guide

## 🚀 Production Deployment Steps

### 1. Upload Files via FTPS
- Upload entire `dist/` directory contents to your web root
- Ensure all files are uploaded completely
- Verify file permissions (644 for files, 755 for directories)

### 2. Configure Domain
- Point your domain to the `public/` directory
- Or use .htaccess redirect (already included)

### 3. Initialize Databases
- Run: `php init-production.php` (one time only)
- This creates all required SQLite databases

### 4. Verify Installation
- Visit: https://boot.responsive.sk
- Check: https://boot.responsive.sk/blog
- Verify: All pages load correctly

## 📁 Directory Structure
```
/
├── public/           # Web root (point domain here)
├── src/             # Application source
├── config/          # Configuration files
├── vendor/          # Production dependencies only
├── var/             # Storage, cache, logs
├── .htaccess        # Production web server config
├── .env             # Production environment
└── init-production.php  # Database initialization
```

## 🔒 Security Features
- Sensitive directories blocked via .htaccess
- Security headers enabled
- File upload restrictions
- SQLite databases outside web root

## ⚡ Performance Features
- Optimized autoloader
- Gzip compression enabled
- Static file caching
- Minimal production dependencies

## 🛠️ Troubleshooting
- Check file permissions: 644/755
- Verify .htaccess is uploaded
- Ensure var/ directory is writable
- Check error logs in var/logs/

## 📊 Build Statistics
- Optimized for shared hosting
- Production dependencies only
- Security hardened
- Performance optimized