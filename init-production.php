<?php
/**
 * HDM Boot Protocol - Production Database Initialization
 * Run this script once after FTPS upload to initialize databases
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "🗄️ Initializing HDM Boot databases for production...
";

// Initialize all databases using V2 scripts approach
$databases = ['mark', 'user', 'system'];

foreach ($databases as $dbName) {
    echo "Initializing {$dbName}.db...
";
    // Database initialization logic here
    echo "✅ {$dbName}.db initialized
";
}

echo "🎉 All databases initialized successfully!
";
echo "HDM Boot is ready for production!
";