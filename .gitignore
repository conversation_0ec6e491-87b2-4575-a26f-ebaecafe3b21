# Environment files
.env
.env.local
.env.production
.env.staging

# Composer
/vendor/
composer.lock

# Runtime files
/var/logs/*.log
/var/cache/*
/var/storage/*.db
/var/uploads/*
/var/sessions/*

# Orbit databases (runtime data)
/var/orbit/*.db
/var/orbit/*.sqlite

# Keep directory structure
!/var/logs/.gitkeep
!/var/cache/.gitkeep
!/var/storage/.gitkeep
!/var/uploads/.gitkeep
!/var/sessions/.gitkeep
!/var/orbit/.gitkeep

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Test files
test-*.php
phpunit.xml

# Development packages
/packages/

# Backup files
*.bak
*.backup
*.old

# Coverage reports
/var/coverage/

# Temporary files
*.tmp
*.temp

# Example projects and cloned modules
modules/Optional/slim-example-project-master/
modules/Optional/Authentication/
modules/Optional/Authorization/
modules/Optional/Validation/

# Build directories
/build/
*.zip

# Frontend development
/resources/themes/*/node_modules/
/resources/themes/*/dist/
/resources/themes/*/.vite/
/resources/themes/*/pnpm-lock.yaml
/resources/themes/*/package-lock.json
/resources/themes/*/yarn.lock

# Theme assets (built files are committed)
# /public/themes/*/css/
# /public/themes/*/js/
