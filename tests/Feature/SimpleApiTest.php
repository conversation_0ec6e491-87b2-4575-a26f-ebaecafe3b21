<?php

declare(strict_types=1);

namespace HdmBoot\Tests\Feature;

use HdmBoot\Tests\TestCase;

/**
 * Simple API tests to verify basic functionality.
 */
class SimpleApiTest extends TestCase
{
    /**
     * @covers \HdmBoot\Modules\Core\Security\Actions\LoginAction
     */
    public function testLoginEndpoint(): void
    {
        $request = $this->createJsonRequest('POST', '/api/auth/login', [
            'email'    => '<EMAIL>',
            'password' => 'Password123',
        ]);

        $response = $this->executeRequest($request);
        $data = $this->assertJsonResponse($response, 200, true);

        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('token', $data['data']);
        $this->assertIsString($data['data']['token']);
    }

    /**
     * @covers \HdmBoot\Modules\Core\Security\Actions\MeAction
     */
    public function testMeEndpoint(): void
    {
        $token = $this->loginAndGetToken();
        $request = $this->createAuthenticatedRequest('GET', '/api/auth/me', $token);

        $response = $this->executeRequest($request);
        $data = $this->assertJsonResponse($response, 200, true);

        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('user', $data['data']);
        $this->assertSame('<EMAIL>', $data['data']['user']['email']);
    }

    /**
     * @covers \HdmBoot\Modules\Core\User\Actions\ListUsersAction
     */
    public function testUsersEndpoint(): void
    {
        $token = $this->loginAndGetToken();
        $request = $this->createAuthenticatedRequest('GET', '/api/users', $token);

        $response = $this->executeRequest($request);
        $data = $this->assertJsonResponse($response, 200, true);

        $this->assertArrayHasKey('data', $data);
        $this->assertIsArray($data['data']);
        $this->assertGreaterThanOrEqual(1, count($data['data']));
    }

    /**
     * @covers \HdmBoot\Modules\Core\User\Actions\ListUsersAction
     */
    public function testUnauthorizedAccess(): void
    {
        $request = $this->createRequest('GET', '/api/users');

        $response = $this->executeRequest($request);
        $this->assertErrorResponse($response, 401);
    }

    /**
     * @covers \HdmBoot\Modules\Core\Security\Actions\LoginAction
     */
    public function testInvalidLogin(): void
    {
        $request = $this->createJsonRequest('POST', '/api/auth/login', [
            'email'    => '<EMAIL>',
            'password' => 'WrongPassword',
        ]);

        $response = $this->executeRequest($request);
        $this->assertErrorResponse($response, 401);
    }
}
