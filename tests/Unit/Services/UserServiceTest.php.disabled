<?php

declare(strict_types=1);

namespace HdmBoot\Tests\Unit\Services;

use HdmBoot\Modules\Core\User\Domain\Entities\User;
use HdmBoot\Modules\Core\User\Domain\ValueObjects\UserId;
use HdmBoot\Modules\Core\User\Repository\UserRepositoryInterface;
use HdmBoot\Modules\Core\User\Services\UserService;
use HdmBoot\Modules\Core\User\Exceptions\UserAlreadyExistsException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

/**
 * Test UserService - Enterprise User Management.
 */
#[CoversClass(UserService::class)]
final class UserServiceTest extends TestCase
{
    private UserService $userService;
    private UserRepositoryInterface $repository;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = $this->createMock(UserRepositoryInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->userService = new UserService($this->repository, $this->logger);
    }

    #[Test]
    public function serviceCanCreateUser(): void
    {
        $email = '<EMAIL>';
        $name = 'Test User';
        $password = 'Password123';
        $role = 'user';

        // Mock repository to return false for email exists check
        $this->repository->expects($this->once())
            ->method('emailExists')
            ->with($email)
            ->willReturn(false);

        // Mock repository save method to return user array
        $expectedUserData = [
            'id' => 'test-user-123',
            'email' => $email,
            'name' => $name,
            'role' => $role,
            'status' => 'active',
            'created_at' => '2025-06-22 12:00:00'
        ];

        $this->repository->expects($this->once())
            ->method('save')
            ->with($this->isType('array'))
            ->willReturn($expectedUserData);

        $user = $this->userService->createUser($email, $name, $password, $role);

        $this->assertIsArray($user);
        $this->assertSame($email, $user['email']);
        $this->assertSame($name, $user['name']);
        $this->assertSame($role, $user['role']);
        $this->assertSame('active', $user['status']);
    }

    #[Test]
    public function serviceThrowsExceptionForDuplicateEmail(): void
    {
        $email = '<EMAIL>';
        $name = 'Test User';
        $password = 'Password123';

        // Mock repository to return true for email exists check
        $this->repository->expects($this->once())
            ->method('emailExists')
            ->with($email)
            ->willReturn(true);

        // Save should not be called
        $this->repository->expects($this->never())
            ->method('save');

        $this->expectException(UserAlreadyExistsException::class);
        $this->expectExceptionMessage("User with email '<EMAIL>' already exists");

        $this->userService->createUser($email, $name, $password);
    }

    #[Test]
    public function serviceCanGetUserById(): void
    {
        $userId = UserId::generate();
        $user = $this->createMockUser($userId);

        $this->repository->expects($this->once())
            ->method('findById')
            ->with($userId)
            ->willReturn($user);

        $result = $this->userService->getUserById($userId->toString());

        $this->assertSame($user, $result);
    }

    #[Test]
    public function serviceReturnsNullForInvalidUserId(): void
    {
        $invalidId = 'invalid-uuid';

        // Repository should not be called for invalid UUID
        $this->repository->expects($this->never())
            ->method('findById');

        $result = $this->userService->getUserById($invalidId);

        $this->assertNull($result);
    }

    #[Test]
    public function serviceCanGetUserByEmail(): void
    {
        $email = '<EMAIL>';
        $user = $this->createMockUser();

        $this->repository->expects($this->once())
            ->method('findByEmail')
            ->with($email)
            ->willReturn($user);

        $result = $this->userService->getUserByEmail($email);

        $this->assertSame($user, $result);
    }

    #[Test]
    public function serviceCanAuthenticateUser(): void
    {
        $email = '<EMAIL>';
        $password = 'Password123';
        $user = $this->createMockUser();

        // Mock user methods
        $user->method('verifyPassword')->with($password)->willReturn(true);
        $user->method('isActive')->willReturn(true);

        $this->repository->expects($this->once())
            ->method('findByEmail')
            ->with($email)
            ->willReturn($user);

        $this->repository->expects($this->once())
            ->method('save')
            ->with($user);

        $result = $this->userService->authenticate($email, $password);

        $this->assertSame($user, $result);
    }

    #[Test]
    public function serviceThrowsExceptionForInvalidCredentials(): void
    {
        $email = '<EMAIL>';
        $password = 'WrongPassword';

        $this->repository->expects($this->once())
            ->method('findByEmail')
            ->with($email)
            ->willReturn(null);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid credentials');

        $this->userService->authenticate($email, $password);
    }

    #[Test]
    public function serviceThrowsExceptionForInactiveUser(): void
    {
        $email = '<EMAIL>';
        $password = 'Password123';
        $user = $this->createMockUser();

        // Mock user as inactive
        $user->method('isActive')->willReturn(false);

        $this->repository->expects($this->once())
            ->method('findByEmail')
            ->with($email)
            ->willReturn($user);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('User account is not active');

        $this->userService->authenticate($email, $password);
    }

    #[Test]
    public function serviceValidatesUserInput(): void
    {
        // Test empty email
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Email is required');

        $this->userService->createUser('', 'Test User', 'Password123');
    }

    #[Test]
    public function serviceValidatesEmailFormat(): void
    {
        $this->repository->method('emailExists')->willReturn(false);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid email format');

        $this->userService->createUser('invalid-email', 'Test User', 'Password123');
    }

    #[Test]
    public function serviceValidatesPasswordStrength(): void
    {
        $this->repository->method('emailExists')->willReturn(false);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Password must contain at least one lowercase letter');

        $this->userService->createUser('<EMAIL>', 'Test User', 'PASSWORD123');
    }

    #[Test]
    public function serviceHasPermissionChecks(): void
    {
        $adminUser = $this->createMockUser();
        $adminUser->method('isAdmin')->willReturn(true);
        $adminUser->method('isEditor')->willReturn(false);

        $this->assertTrue($this->userService->hasPermission($adminUser, 'admin.access'));
        $this->assertTrue($this->userService->hasPermission($adminUser, 'user.delete'));
        $this->assertTrue($this->userService->hasPermission($adminUser, 'user.manage'));

        $regularUser = $this->createMockUser();
        $regularUser->method('isAdmin')->willReturn(false);
        $regularUser->method('isEditor')->willReturn(false);

        $this->assertTrue($this->userService->hasPermission($regularUser, 'user.view'));
        $this->assertFalse($this->userService->hasPermission($regularUser, 'admin.access'));
        $this->assertFalse($this->userService->hasPermission($regularUser, 'user.delete'));
    }

    /**
     * Create mock user for testing.
     */
    private function createMockUser(?UserId $userId = null): User
    {
        $user = $this->createMock(User::class);
        
        if ($userId !== null) {
            $user->method('getId')->willReturn($userId);
        }
        
        return $user;
    }
}
