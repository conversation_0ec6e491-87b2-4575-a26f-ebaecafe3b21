<?php

declare(strict_types=1);

/**
 * HDM Boot Protocol - PDO DI Container Fix V2
 * 
 * Fixes PDO dependency injection issue:
 * - EventStore requires PDO but it's not defined in container
 * - Creates proper PDO service definition
 * - Uses SQLite database for EventStore
 * - Maintains security and performance
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use HdmBoot\SharedKernel\Services\PathsFactory;

echo "🔧 HDM Boot Protocol - PDO DI Container Fix\n";
echo "==========================================\n\n";

class PDOContainerFixer
{
    private $paths;
    private $changes = [];
    
    public function __construct()
    {
        $this->paths = PathsFactory::create();
    }
    
    public function fix(): void
    {
        echo "🚀 Starting PDO DI container fix...\n\n";
        
        $this->analyzeCurrentProblem();
        $this->createPDOServiceDefinition();
        $this->updateContainerConfiguration();
        $this->testPDOConnection();
        $this->generateReport();
        
        echo "✅ PDO DI container fix completed!\n";
    }
    
    private function analyzeCurrentProblem(): void
    {
        echo "🔍 Analyzing PDO DI container problem...\n";
        
        echo "📋 Problem Analysis:\n";
        echo "   • EventStore config requires \\PDO::class\n";
        echo "   • PDO service not defined in DI container\n";
        echo "   • DatabaseEventStore cannot be instantiated\n";
        echo "   • Application fails to start\n";
        
        echo "\n📋 Required Solution:\n";
        echo "   • Define PDO service in container\n";
        echo "   • Use SQLite database for EventStore\n";
        echo "   • Maintain security and performance\n";
        echo "   • Follow HDM Boot architecture\n";
        
        echo "\n";
    }
    
    private function createPDOServiceDefinition(): void
    {
        echo "🗄️ Creating PDO service definition...\n";
        
        $pdoServiceConfig = $this->generatePDOServiceConfig();
        $configPath = $this->paths->config('services/pdo.php');
        
        // Ensure services directory exists
        $servicesDir = dirname($configPath);
        if (!is_dir($servicesDir)) {
            mkdir($servicesDir, 0755, true);
            echo "   📁 Created services directory\n";
        }
        
        if (file_put_contents($configPath, $pdoServiceConfig)) {
            echo "   ✅ Created: config/services/pdo.php\n";
            $this->changes[] = "Created PDO service configuration";
        } else {
            echo "   ❌ Failed to create PDO service config\n";
        }
        
        echo "\n";
    }
    
    private function updateContainerConfiguration(): void
    {
        echo "⚙️ Updating container configuration...\n";
        
        $containerPath = $this->paths->config('container.php');
        $content = file_get_contents($containerPath);
        
        // Check if PDO services are already included
        if (strpos($content, 'services/pdo.php') !== false) {
            echo "   ℹ️  PDO services already included in container\n";
            return;
        }
        
        // Add PDO services to the array_merge
        $oldMerge = "// Core infrastructure services (non-module)\n    // Note: Session services moved to Session module\n    require __DIR__ . '/services/logging.php',\n    require __DIR__ . '/services/events.php',\n    require __DIR__ . '/services/monitoring.php',";
        
        $newMerge = "// Core infrastructure services (non-module)\n    // Note: Session services moved to Session module\n    require __DIR__ . '/services/logging.php',\n    require __DIR__ . '/services/events.php',\n    require __DIR__ . '/services/monitoring.php',\n    require __DIR__ . '/services/pdo.php',";
        
        $updatedContent = str_replace($oldMerge, $newMerge, $content);
        
        if ($updatedContent !== $content) {
            if (file_put_contents($containerPath, $updatedContent)) {
                echo "   ✅ Updated container.php to include PDO services\n";
                $this->changes[] = "Updated container configuration";
            } else {
                echo "   ❌ Failed to update container configuration\n";
            }
        } else {
            echo "   ⚠️  Container configuration not updated (pattern not found)\n";
            
            // Manual approach - show what needs to be added
            echo "   📋 Manual update needed:\n";
            echo "      Add this line to container.php array_merge:\n";
            echo "      require __DIR__ . '/services/pdo.php',\n";
        }
        
        echo "\n";
    }
    
    private function testPDOConnection(): void
    {
        echo "🧪 Testing PDO connection...\n";
        
        try {
            // Test PDO connection directly
            $eventStorePath = $this->paths->storage('eventstore.db');
            $dsn = "sqlite:{$eventStorePath}";
            
            $pdo = new \PDO($dsn, null, null, [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
            ]);
            
            echo "   ✅ PDO connection successful\n";
            echo "   📄 Database: {$eventStorePath}\n";
            
            // Test basic query
            $stmt = $pdo->query("SELECT sqlite_version()");
            $version = $stmt->fetchColumn();
            echo "   📊 SQLite version: {$version}\n";
            
            $this->changes[] = "Verified PDO connection works";
            
        } catch (\Throwable $e) {
            echo "   ❌ PDO connection failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    private function generatePDOServiceConfig(): string
    {
        return <<<PHP
<?php

declare(strict_types=1);

/**
 * PDO Service Configuration for HDM Boot Protocol
 * 
 * Provides PDO database connection for EventStore and other components.
 * Uses SQLite for lightweight, secure, and performant storage.
 */

use DI\Container;
use ResponsiveSk\Slim4Paths\Paths;

return [
    // PDO Service Definition
    \\PDO::class => function (Container \$container): \\PDO {
        \$paths = \$container->get(Paths::class);
        if (!\$paths instanceof Paths) {
            throw new \\RuntimeException('Paths service not properly configured');
        }
        
        // Use EventStore SQLite database
        \$eventStorePath = \$paths->storage('eventstore.db');
        \$dsn = "sqlite:{\$eventStorePath}";
        
        // Ensure storage directory exists
        \$storageDir = dirname(\$eventStorePath);
        if (!is_dir(\$storageDir)) {
            mkdir(\$storageDir, 0700, true);
        }
        
        try {
            \$pdo = new \\PDO(\$dsn, null, null, [
                \\PDO::ATTR_ERRMODE => \\PDO::ERRMODE_EXCEPTION,
                \\PDO::ATTR_DEFAULT_FETCH_MODE => \\PDO::FETCH_ASSOC,
                \\PDO::ATTR_EMULATE_PREPARES => false,
                \\PDO::ATTR_TIMEOUT => 30,
            ]);
            
            // Optimize SQLite for performance
            \$pdo->exec('PRAGMA journal_mode=WAL');
            \$pdo->exec('PRAGMA synchronous=NORMAL');
            \$pdo->exec('PRAGMA cache_size=10000');
            \$pdo->exec('PRAGMA temp_store=MEMORY');
            
            return \$pdo;
            
        } catch (\\PDOException \$e) {
            throw new \\RuntimeException(
                "Failed to create PDO connection to EventStore database: " . \$e->getMessage(),
                0,
                \$e
            );
        }
    },
    
    // Alternative: PDO Factory for multiple databases
    'pdo.factory' => function (Container \$container): \\Closure {
        return function (string \$database = 'eventstore') use (\$container): \\PDO {
            \$paths = \$container->get(Paths::class);
            if (!\$paths instanceof Paths) {
                throw new \\RuntimeException('Paths service not properly configured');
            }
            
            \$dbPath = \$paths->storage("{\$database}.db");
            \$dsn = "sqlite:{\$dbPath}";
            
            // Ensure storage directory exists
            \$storageDir = dirname(\$dbPath);
            if (!is_dir(\$storageDir)) {
                mkdir(\$storageDir, 0700, true);
            }
            
            try {
                \$pdo = new \\PDO(\$dsn, null, null, [
                    \\PDO::ATTR_ERRMODE => \\PDO::ERRMODE_EXCEPTION,
                    \\PDO::ATTR_DEFAULT_FETCH_MODE => \\PDO::FETCH_ASSOC,
                    \\PDO::ATTR_EMULATE_PREPARES => false,
                    \\PDO::ATTR_TIMEOUT => 30,
                ]);
                
                // Optimize SQLite for performance
                \$pdo->exec('PRAGMA journal_mode=WAL');
                \$pdo->exec('PRAGMA synchronous=NORMAL');
                \$pdo->exec('PRAGMA cache_size=10000');
                \$pdo->exec('PRAGMA temp_store=MEMORY');
                
                return \$pdo;
                
            } catch (\\PDOException \$e) {
                throw new \\RuntimeException(
                    "Failed to create PDO connection to {\$database} database: " . \$e->getMessage(),
                    0,
                    \$e
                );
            }
        };
    },
];
PHP;
    }
    
    private function generateReport(): void
    {
        echo "📊 PDO DI CONTAINER FIX REPORT\n";
        echo "==============================\n\n";
        
        echo "✅ CHANGES MADE (" . count($this->changes) . "):\n";
        foreach ($this->changes as $change) {
            echo "   • {$change}\n";
        }
        
        echo "\n🔧 PDO SERVICE CONFIGURATION:\n";
        echo "=============================\n";
        echo "📄 File: config/services/pdo.php\n";
        echo "🗄️ Database: var/storage/eventstore.db\n";
        echo "🔧 Driver: SQLite with WAL mode\n";
        echo "⚡ Optimizations: Cache, synchronous, temp storage\n";
        echo "🔒 Security: 700 permissions on storage directory\n";
        
        echo "\n🎯 BENEFITS:\n";
        echo "============\n";
        echo "   • ✅ EventStore can now be instantiated\n";
        echo "   • ✅ PDO service available for other components\n";
        echo "   • ✅ SQLite optimized for performance\n";
        echo "   • ✅ Secure storage directory permissions\n";
        echo "   • ✅ Consistent with HDM Boot architecture\n";
        
        echo "\n📋 USAGE:\n";
        echo "=========\n";
        echo "// Get PDO from container\n";
        echo "\$pdo = \$container->get(\\PDO::class);\n\n";
        echo "// Get PDO factory for multiple databases\n";
        echo "\$pdoFactory = \$container->get('pdo.factory');\n";
        echo "\$eventStorePdo = \$pdoFactory('eventstore');\n";
        echo "\$customPdo = \$pdoFactory('custom_db');\n";
        
        echo "\n🧪 TESTING:\n";
        echo "===========\n";
        echo "1. Test application startup (should not fail)\n";
        echo "2. Test EventStore functionality\n";
        echo "3. Verify database file creation\n";
        echo "4. Check SQLite optimizations\n";
        
        echo "\n";
    }
}

try {
    $fixer = new PDOContainerFixer();
    $fixer->fix();

} catch (\Throwable $e) {
    echo "💥 PDO FIX FAILED: " . $e->getMessage() . "\n";
    echo "📍 Location: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}
