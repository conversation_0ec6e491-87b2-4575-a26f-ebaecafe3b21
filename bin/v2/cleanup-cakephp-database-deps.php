<?php

declare(strict_types=1);

/**
 * HDM Boot Protocol - CakePHP Database Dependencies Cleanup V2
 * 
 * Removes all CakePHP Database dependencies and references:
 * - Removes cakephp/database from composer.json
 * - Removes CakePHPDatabaseManager (strategic decision changed)
 * - Cleans up all CakePHP Database imports
 * - Keeps cakephp/validation (security critical)
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use HdmBoot\SharedKernel\Services\PathsFactory;

echo "🧹 HDM Boot Protocol - CakePHP Database Dependencies Cleanup\n";
echo "===========================================================\n\n";

class CakePHPDatabaseCleaner
{
    private $paths;
    private $changes = [];
    private $errors = [];
    
    public function __construct()
    {
        $this->paths = PathsFactory::create();
    }
    
    public function cleanup(): void
    {
        echo "🚀 Starting CakePHP Database dependencies cleanup...\n\n";
        
        $this->analyzeCurrentState();
        $this->removeCakePHPDatabaseManager();
        $this->cleanupDatabaseConfig();
        $this->removeCakePHPDependency();
        $this->updateAutoloader();
        $this->verifyCleanup();
        $this->generateReport();
        
        echo "✅ CakePHP Database cleanup completed!\n";
    }
    
    private function analyzeCurrentState(): void
    {
        echo "🔍 Analyzing CakePHP Database usage...\n";
        
        echo "📋 Files to be cleaned:\n";
        
        $filesToCheck = [
            'src/Modules/Core/Database/Infrastructure/Services/CakePHPDatabaseManager.php' => 'REMOVE',
            'src/Modules/Core/Database/config.php' => 'CLEAN',
            'composer.json' => 'UPDATE'
        ];
        
        foreach ($filesToCheck as $file => $action) {
            $fullPath = $this->paths->path($file);
            if (file_exists($fullPath)) {
                echo "   📄 {$file}: {$action}\n";
            } else {
                echo "   ❌ {$file}: NOT FOUND\n";
            }
        }
        
        echo "\n📋 Dependencies to remove:\n";
        echo "   ❌ cakephp/database (~2.5MB)\n";
        echo "   ✅ cakephp/validation (KEEP - security critical)\n";
        
        echo "\n";
    }
    
    private function removeCakePHPDatabaseManager(): void
    {
        echo "❌ Removing CakePHPDatabaseManager...\n";
        
        $managerPath = $this->paths->src('Modules/Core/Database/Infrastructure/Services/CakePHPDatabaseManager.php');
        
        if (file_exists($managerPath)) {
            if (unlink($managerPath)) {
                echo "   ✅ Removed: CakePHPDatabaseManager.php\n";
                $this->changes[] = "Removed CakePHPDatabaseManager.php";
            } else {
                echo "   ❌ Failed to remove CakePHPDatabaseManager.php\n";
                $this->errors[] = "Failed to remove CakePHPDatabaseManager.php";
            }
        } else {
            echo "   ℹ️  CakePHPDatabaseManager.php not found (already removed)\n";
        }
        
        echo "\n";
    }
    
    private function cleanupDatabaseConfig(): void
    {
        echo "⚙️ Cleaning Database module config...\n";
        
        $configPath = $this->paths->src('Modules/Core/Database/config.php');
        
        if (!file_exists($configPath)) {
            echo "   ❌ Database config not found\n";
            return;
        }
        
        $content = file_get_contents($configPath);
        $originalContent = $content;
        
        // Remove CakePHP imports
        $content = preg_replace('/use.*CakePHPDatabaseManager.*;\n/', '', $content);
        
        // Remove CakePHP service definition
        $content = preg_replace('/\s*CakePHPDatabaseManager::class.*?\],\s*/s', '', $content);
        
        // Remove cakephp from supported managers
        $content = str_replace(
            "'cakephp' => [
                'description' => 'CakePHP Database manager (strategic)',
                'manager' => 'CakePHPDatabaseManager',
                'usage' => 'Complex queries, schema operations (when needed)'
            ],",
            '',
            $content
        );
        
        // Update default manager comment
        $content = str_replace(
            '// Primary approach: Native PDO SQLite (used by V2 scripts)
        \'default_manager\' => \'sqlite_pdo\',',
            '// Native PDO SQLite approach (V2 scripts)
        \'default_manager\' => \'sqlite_pdo\',',
            $content
        );
        
        // Clean up description
        $content = str_replace(
            'description\' => \'HDM Boot Database Module with Hybrid Architecture\',',
            'description\' => \'HDM Boot Database Module with Native SQLite\',',
            $content
        );
        
        if ($content !== $originalContent) {
            if (file_put_contents($configPath, $content)) {
                echo "   ✅ Cleaned Database config\n";
                $this->changes[] = "Cleaned Database module configuration";
            } else {
                echo "   ❌ Failed to clean Database config\n";
                $this->errors[] = "Failed to clean Database configuration";
            }
        } else {
            echo "   ℹ️  Database config already clean\n";
        }
        
        echo "\n";
    }
    
    private function removeCakePHPDependency(): void
    {
        echo "📦 Removing cakephp/database dependency...\n";
        
        // Use composer remove command
        $command = "cd " . escapeshellarg($this->paths->base()) . " && composer remove cakephp/database --no-interaction";
        
        echo "   🔧 Executing: composer remove cakephp/database\n";
        
        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "   ✅ Successfully removed cakephp/database\n";
            $this->changes[] = "Removed cakephp/database dependency";
            
            // Show relevant output
            foreach ($output as $line) {
                if (strpos($line, 'Removing') !== false || strpos($line, 'Writing') !== false) {
                    echo "   📋 {$line}\n";
                }
            }
        } else {
            echo "   ⚠️  Composer remove failed (might already be removed)\n";
            foreach ($output as $line) {
                if (strpos($line, 'not found') !== false || strpos($line, 'not installed') !== false) {
                    echo "   ℹ️  {$line}\n";
                }
            }
        }
        
        echo "\n";
    }
    
    private function updateAutoloader(): void
    {
        echo "🔄 Updating autoloader...\n";
        
        $command = "cd " . escapeshellarg($this->paths->path()) . " && composer dump-autoload --optimize";
        
        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "   ✅ Autoloader optimized\n";
            $this->changes[] = "Optimized autoloader";
        } else {
            echo "   ❌ Failed to optimize autoloader\n";
            $this->errors[] = "Failed to optimize autoloader";
        }
        
        echo "\n";
    }
    
    private function verifyCleanup(): void
    {
        echo "🧪 Verifying cleanup...\n";
        
        // Check composer.json
        $composerPath = $this->paths->path('composer.json');
        if (file_exists($composerPath)) {
            $composer = json_decode(file_get_contents($composerPath), true);
            $dependencies = $composer['require'] ?? [];
            
            if (!isset($dependencies['cakephp/database'])) {
                echo "   ✅ cakephp/database removed from composer.json\n";
            } else {
                echo "   ❌ cakephp/database still in composer.json\n";
                $this->errors[] = "cakephp/database still in composer.json";
            }
            
            if (isset($dependencies['cakephp/validation'])) {
                echo "   ✅ cakephp/validation preserved in composer.json\n";
            } else {
                echo "   ⚠️  cakephp/validation missing from composer.json\n";
            }
        }
        
        // Check vendor directory
        $cakeDatabasePath = $this->paths->path('vendor/cakephp/database');
        if (!is_dir($cakeDatabasePath)) {
            echo "   ✅ cakephp/database removed from vendor\n";
        } else {
            echo "   ⚠️  cakephp/database still in vendor\n";
        }
        
        $cakeValidationPath = $this->paths->path('vendor/cakephp/validation');
        if (is_dir($cakeValidationPath)) {
            echo "   ✅ cakephp/validation preserved in vendor\n";
        } else {
            echo "   ❌ cakephp/validation missing from vendor\n";
            $this->errors[] = "cakephp/validation missing from vendor";
        }
        
        // Check that CakePHPDatabaseManager is gone
        $managerPath = $this->paths->src('Modules/Core/Database/Infrastructure/Services/CakePHPDatabaseManager.php');
        if (!file_exists($managerPath)) {
            echo "   ✅ CakePHPDatabaseManager.php removed\n";
        } else {
            echo "   ❌ CakePHPDatabaseManager.php still exists\n";
            $this->errors[] = "CakePHPDatabaseManager.php still exists";
        }
        
        echo "\n";
    }
    
    private function generateReport(): void
    {
        echo "📊 CAKEPHP DATABASE CLEANUP REPORT\n";
        echo "==================================\n\n";
        
        echo "✅ CHANGES MADE (" . count($this->changes) . "):\n";
        foreach ($this->changes as $change) {
            echo "   • {$change}\n";
        }
        
        if (!empty($this->errors)) {
            echo "\n❌ ERRORS (" . count($this->errors) . "):\n";
            foreach ($this->errors as $error) {
                echo "   • {$error}\n";
            }
        }
        
        echo "\n🎯 CLEANUP SUMMARY:\n";
        echo "===================\n";
        echo "❌ REMOVED:\n";
        echo "   • cakephp/database dependency (~2.5MB)\n";
        echo "   • CakePHPDatabaseManager.php\n";
        echo "   • CakePHP Database imports and references\n";
        echo "   • CakePHP service definitions\n";
        
        echo "\n✅ PRESERVED:\n";
        echo "   • cakephp/validation dependency (~0.5MB)\n";
        echo "   • AuthenticationValidator functionality\n";
        echo "   • Native PDO SQLite managers\n";
        echo "   • V2 scripts functionality\n";
        
        echo "\n📊 BENEFITS ACHIEVED:\n";
        echo "====================\n";
        echo "   • Bundle size reduction: ~2.5MB\n";
        echo "   • Eliminated CakePHP Database PHPStan errors\n";
        echo "   • Cleaner architecture (pure PDO SQLite)\n";
        echo "   • Faster autoloader (fewer classes)\n";
        echo "   • Consistent with V2 scripts approach\n";
        
        echo "\n📋 NEXT STEPS:\n";
        echo "==============\n";
        echo "1. Run PHPStan to verify CakePHP errors are gone\n";
        echo "2. Test V2 scripts functionality\n";
        echo "3. Test authentication (cakephp/validation)\n";
        echo "4. Fix remaining template type issues\n";
        echo "5. Create production build\n";
        
        echo "\n💡 ARCHITECTURE DECISION:\n";
        echo "=========================\n";
        echo "Changed from hybrid approach to pure PDO SQLite:\n";
        echo "• Simpler architecture\n";
        echo "• Better performance\n";
        echo "• Fewer dependencies\n";
        echo "• Consistent with V2 scripts\n";
        echo "• CakePHP validation preserved for security\n";
        
        echo "\n";
    }
}

try {
    $cleaner = new CakePHPDatabaseCleaner();
    $cleaner->cleanup();
    
} catch (\Throwable $e) {
    echo "💥 CLEANUP FAILED: " . $e->getMessage() . "\n";
    echo "📍 Location: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}
