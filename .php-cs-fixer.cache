{"php": "8.4.8", "version": "3.75.0:v3.75.0#399a128ff2fdaf4281e4e79b755693286cdf325c", "indent": "    ", "lineEnding": "\n", "rules": {"binary_operator_spaces": {"default": "single_space", "operators": {"=>": "align"}}, "blank_line_after_opening_tag": true, "blank_line_between_import_groups": true, "blank_lines_before_namespace": true, "braces_position": {"allow_single_line_empty_anonymous_classes": true}, "class_definition": {"inline_constructor_arguments": false, "space_before_parenthesis": true}, "compact_nullable_type_declaration": true, "declare_equal_normalize": true, "lowercase_cast": true, "lowercase_static_reference": true, "new_with_parentheses": true, "no_blank_lines_after_class_opening": true, "no_extra_blank_lines": {"tokens": ["curly_brace_block", "extra", "parenthesis_brace_block", "square_brace_block", "throw", "use"]}, "no_leading_import_slash": true, "no_whitespace_in_blank_line": true, "ordered_class_elements": {"order": ["use_trait"]}, "ordered_imports": {"sort_algorithm": "alpha"}, "return_type_declaration": true, "short_scalar_cast": true, "single_import_per_statement": {"group_to_single_imports": false}, "single_space_around_construct": {"constructs_followed_by_a_single_space": ["abstract", "as", "case", "catch", "class", "const_import", "do", "else", "elseif", "final", "finally", "for", "foreach", "function", "function_import", "if", "insteadof", "interface", "namespace", "new", "private", "protected", "public", "static", "switch", "trait", "try", "use", "use_lambda", "while"], "constructs_preceded_by_a_single_space": ["as", "else", "elseif", "use_lambda"]}, "single_trait_insert_per_statement": true, "ternary_operator_spaces": true, "unary_operator_spaces": true, "visibility_required": true, "blank_line_after_namespace": true, "constant_case": true, "control_structure_braces": true, "control_structure_continuation_position": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"on_multiline": "ensure_fully_multiline"}, "no_break_comment": true, "no_closing_tag": true, "no_multiple_statements_per_line": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": true, "single_line_after_imports": true, "spaces_inside_parentheses": true, "statement_indentation": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "encoding": true, "full_opening_tag": true, "octal_notation": true, "clean_namespace": true, "no_unset_cast": true, "assign_null_coalescing_to_coalesce_equal": true, "normalize_index_brace": true, "heredoc_indentation": true, "no_whitespace_before_comma_in_array": true, "trailing_comma_in_multiline": true, "list_syntax": true, "ternary_to_null_coalescing": true, "array_syntax": {"syntax": "short"}, "blank_line_before_statement": {"statements": ["return"]}, "cast_spaces": true, "class_attributes_separation": {"elements": {"method": "one", "property": "one"}}, "concat_space": {"spacing": "one"}, "declare_strict_types": true, "function_typehint_space": true, "include": true, "increment_style": true, "magic_constant_casing": true, "native_function_casing": true, "new_with_braces": true, "no_blank_lines_after_phpdoc": true, "no_empty_comment": true, "no_empty_phpdoc": true, "no_empty_statement": true, "no_leading_namespace_whitespace": true, "no_mixed_echo_print": {"use": "echo"}, "no_multiline_whitespace_around_double_arrow": true, "no_short_bool_cast": true, "no_singleline_whitespace_before_semicolons": true, "no_spaces_around_offset": true, "no_trailing_comma_in_list_call": true, "no_trailing_comma_in_singleline_array": true, "no_unneeded_control_parentheses": true, "no_unused_imports": true, "object_operator_without_whitespace": true, "phpdoc_indent": true, "phpdoc_inline_tag_normalizer": true, "phpdoc_no_access": true, "phpdoc_no_alias_tag": true, "phpdoc_no_empty_return": true, "phpdoc_no_package": true, "phpdoc_no_useless_inheritdoc": true, "phpdoc_return_self_reference": true, "phpdoc_scalar": true, "phpdoc_separation": true, "phpdoc_single_line_var_spacing": true, "phpdoc_summary": true, "phpdoc_to_comment": true, "phpdoc_trim": true, "phpdoc_types": true, "phpdoc_var_without_name": true, "semicolon_after_instruction": true, "single_line_comment_style": {"comment_types": ["hash"]}, "single_quote": true, "space_after_semicolon": {"remove_in_empty_for_expressions": true}, "standardize_not_equals": true, "trim_array_spaces": true, "whitespace_after_comma_in_array": true}, "hashes": {"src/Helpers/SecurePathHelper.php": "a7ab16a87c75c171034f56482ba668c6", "src/Database/DatabaseManager.php": "6ea4bb8c9518e463196a0df852aa3c37", "modules/Core/User/Domain/Entities/User.php": "a71f58541cf4aa19bf60d6eb81589cb8", "modules/Core/User/Domain/ValueObjects/UserId.php": "f7dda08a8f0d1a3ef3cb4d5ab725fe18", "modules/Core/User/Actions/ListUsersAction.php": "640e889417b113b58843af36e777c714", "modules/Core/User/Actions/CreateUserAction.php": "f54e53e7c5192782ab2002658d2bc68e", "modules/Core/User/Actions/GetUserAction.php": "f17e062b6fb8701563edd0f8e5ad9665", "modules/Core/Security/Domain/ValueObjects/JwtToken.php": "264e287f6c0e36f3b988c49e045f1ea3", "modules/Core/Security/Actions/LoginAction.php": "7f2f491a1760a5cbb23549bf8c8bfd8e", "modules/Core/Security/Actions/LogoutAction.php": "79c9e96ca3a2df0c94f732a30fed458a", "modules/Core/Security/Actions/RefreshTokenAction.php": "dde983140f173e590ed46cfef655b715", "modules/Core/Security/Actions/MeAction.php": "1b72e3006ba7ee1ac90075954839b99a", "modules/Core/Security/Enum/SecurityType.php": "d83b8e849331a40f7c22bab8bbb646d2", "modules/Core/Security/config.php": "93189e15fe4ed6ade5cfb335e0bb0f04", "modules/Core/Security/Middleware/AuthenticationMiddleware.php": "71872c2b8b791605ec9f828cc4a3e4dd", "modules/Optional/Security/Exception/SecurityException.php": "2716a017308fe58cae808abd2fe435f2", "modules/Optional/Security/Enum/SecurityType.php": "ebf080ca2521934be2aaf5fa550d1690", "modules/Optional/Security/Login/Repository/LoginLogFinderRepository.php": "4e73943363d8931597c830832a367ad6", "modules/Optional/Security/Login/Service/LoginRequestFinder.php": "17450d114929ccf386b11bde5e5dd8a4", "modules/Optional/Security/Login/Service/SecurityLoginChecker.php": "e76888d091a4b641d9facea2a4b1c3de", "modules/Optional/Security/Captcha/Service/SecurityCaptchaVerifier.php": "8a67038bfdc46352b313fd70de567894", "modules/Optional/Security/Email/Repository/EmailLogFinderRepository.php": "4c8913c5cc933a709e661c46e5e66f0c", "modules/Optional/Security/Email/Service/EmailRequestFinder.php": "839e5a4f1fffd4a4aa70613d5e18ae56", "modules/Optional/Security/Email/Service/SecurityEmailChecker.php": "3dab481fc18c9fa5d61012304906d753", "config/routes/test.php": "6252b6d383901e125a02e6fdff4e93d8", "config/routes/home.php": "7f883cbc67e4a653d0cc01d5ec886f26", "config/routes/api.php": "4a5e826f31a32c985abf94db2638b99b", "src/Shared/Contracts/ModuleInterface.php": "d7cd28d3d18b5d0d69cb323cf77ea222", "src/Shared/Contracts/MiddlewareInterface.php": "c958bf10ddc0f940797303144884f968", "src/Shared/Events/DomainEvent.php": "11eafa3ea86439c89f988dd5b9035188", "src/Shared/Events/EventBootstrap.php": "f516066eb2b2965f0cf5d0a0e1fbeda0", "src/Shared/Events/EventListener.php": "c66c412d7b70e5ee26ed7fe41c3c4f5e", "src/Shared/Events/EventDispatcherInterface.php": "5239b295f7bfa57aaf2c614c47cb3c56", "src/Shared/Events/ModuleEventBus.php": "2b9e76992817f8d9e1a4ce8208d01159", "src/Shared/Events/EventDispatcher.php": "8ab31d2bdc6c56379c17da750be0f0ea", "src/Shared/Modules/ModuleManager.php": "bd3bb4a88665f174effb38f5c9dda195", "src/Shared/Modules/GenericModule.php": "fbe8a95fd6b3e41dade887de12e05264", "modules/Core/Template/Domain/Contracts/TemplateEngineInterface.php": "e8c39fc97b7c685d542c224128501243", "modules/Core/Template/Domain/Contracts/TemplateRendererInterface.php": "31d311b05446cb903efd37bbcc1f387b", "modules/Core/Template/Domain/Services/TemplateService.php": "4b9bc20214863ca448940481300965f2", "modules/Core/Template/Domain/Events/TemplateRenderedEvent.php": "ff489825d578e77403e58c791be2a066", "modules/Core/Template/Domain/ValueObjects/TemplateName.php": "158f0c462f26106554fb8c130e71ebcc", "modules/Core/Template/Domain/ValueObjects/TemplateData.php": "86a00ec21c8303570e774b8f7248dec9", "modules/Core/Template/Application/Actions/RenderTemplateAction.php": "af261d3f9dfd36f275a58f23375a5c6e", "modules/Core/Template/Application/DTOs/RenderTemplateRequest.php": "e7a1d3019f417e8b4e4f24238070579a", "modules/Core/Template/config.php": "ef2ea05e7dc0215d91ddd02212d4a207", "modules/Core/Template/Infrastructure/Engines/PhpTemplateEngine.php": "d639ad648d8f68bda4089d2f155598ff", "modules/Core/Template/Infrastructure/Engines/TwigTemplateEngine.php": "fc63008a60408f5a377bce005e7302c2", "modules/Core/Template/Infrastructure/Services/TemplateRenderer.php": "08a81de75b271e11639de4253dd0e322", "modules/Core/Monitoring/Infrastructure/Actions/HealthCheckAction.php": "5969170e14600577cfd57cf268e3abb9", "modules/Core/Monitoring/Infrastructure/Metrics/PerformanceMonitor.php": "50c73ef85d4693d9fe7a67d369f86d27", "modules/Core/Monitoring/Infrastructure/Bootstrap/MonitoringBootstrap.php": "1cecd1574911e0076a1490a27f40fc84", "modules/Core/Monitoring/Infrastructure/HealthChecks/DatabaseHealthCheck.php": "8f5115177c24932510a706a627883d2c", "modules/Core/Monitoring/Infrastructure/HealthChecks/FilesystemHealthCheck.php": "bde957ae08403f08b738902d4f8d85c9", "modules/Core/Monitoring/Infrastructure/HealthChecks/HealthStatus.php": "8bb68c826c4f734b6e084566b68f80a8", "modules/Core/Monitoring/Infrastructure/HealthChecks/HealthCheckInterface.php": "ba077e996943ec04723267dbd1114bcf", "modules/Core/Monitoring/Infrastructure/HealthChecks/HealthCheckResult.php": "a4b15514eb84f7093d13080b384398b5", "modules/Core/Monitoring/Infrastructure/HealthChecks/HealthCheckReport.php": "10874eadb2ea7fb6c2f969d6be05961d", "modules/Core/Monitoring/Infrastructure/HealthChecks/HealthCheckManager.php": "75997c94f2765aaf9197d23e1f6568d5", "modules/Core/User/Domain/Models/User.php": "33b9df92d95fa1ddb3a56be8f0fcc037", "modules/Core/User/Domain/Services/UserDomainService.php": "74471741e266a823d57a4d719dc73cf9", "modules/Core/User/Domain/Events/UserWasRegistered.php": "6c8fd1b5b1c2684b563a8f65521be067", "modules/Core/User/Domain/Events/UserWasUpdated.php": "bb8a2f07340099a5a4e42f9c6e8a54e3", "modules/Core/User/Actions/Web/ProfilePageAction.php": "768420b6e05792bb9229cac0b23771a1", "modules/Core/User/Contracts/DTOs/UserDataDTO.php": "9bb4193236bebb211caa3b093de842bd", "modules/Core/User/Contracts/Services/UserServiceInterface.php": "f083c9644647fd7ad9bfa5485ce742fc", "modules/Core/User/Contracts/Events/UserModuleEvents.php": "511827c8c398e397c9246602d41a3bdf", "modules/Core/User/UserModule.php": "aadc4c4ebc5f7970c3918c6f744efc0d", "modules/Core/User/Application/Handlers/RegisterUserHandler.php": "5ac2d5cb642041d03bde8346a3d29c89", "modules/Core/User/Application/Handlers/GetUserProfileHandler.php": "469f8cf00a47d6bd72e7af5f9fbebe83", "modules/Core/User/Application/Commands/RegisterUserCommand.php": "14914dc65196062e09104ebc32163121", "modules/Core/User/Application/Commands/UpdateUserCommand.php": "eed44c0a6c0892f0e33b6c07bf407d90", "modules/Core/User/Application/Queries/FindUserByEmailQuery.php": "e8b9137ceec89fc57611b014364959ec", "modules/Core/User/Application/Queries/GetUserProfileQuery.php": "b453ece2fbfef6fd90756a535659d014", "modules/Core/User/Exceptions/UserNotFoundException.php": "4602ac69cabe5b64113818382be647d0", "modules/Core/User/Exceptions/UserAlreadyExistsException.php": "d0061eb79b98753fc5388bbb36c1ccf3", "modules/Core/Security/Domain/DTOs/LoginResult.php": "9a699a5bcc66e34adf4a5c92001adf90", "modules/Core/Security/Domain/DTOs/LoginRequest.php": "3e26d4f390d56724ba2dbcbd18b5ebf1", "modules/Core/Security/Domain/Services/AuthenticationDomainService.php": "0b7d094f044b4641f7764c4f6aea292c", "modules/Core/Security/Actions/Web/LoginSubmitAction.php": "1d0881dda0c6b407f747e515abdbe13e", "modules/Core/Security/Actions/Web/LoginPageAction.php": "0389764fccd0dca1ff3efc15e75efe57", "modules/Core/Security/Actions/Web/LogoutAction.php": "5ef1e77ae29799cf678dfdfcf2021a87", "modules/Core/Security/Exception/ValidationException.php": "f3ba8c4f7f4202505e0cd6e4e8eb82f6", "modules/Core/Security/Contracts/Services/AuthorizationServiceInterface.php": "3629fda20e3b10849aade7f755d0e326", "modules/Core/Security/Contracts/Services/AuthenticationServiceInterface.php": "6deefa1382bed8ab1dc325fcf562677a", "modules/Core/Security/Contracts/Events/SecurityModuleEvents.php": "df2fb8e63817b7c82606d23815c1941d", "modules/Core/Security/Application/Actions/LoginSubmitAction.php": "de583bfe565e1450966572e5c3bfd31e", "modules/Core/Security/Services/AuthenticationValidator.php": "d8858f9c99b3c0d3d2de25db6efa63ca", "modules/Core/Security/Services/SessionService.php": "9978e9ee6f4e5ef604cab6f1c6a5d823", "modules/Core/Security/Services/CsrfService.php": "7d2115d8d2e72a253fbd92e11df83cbb", "modules/Core/Security/Exceptions/SecurityException.php": "1c6883d6ed3df40d5bd95460d52f6a8d", "modules/Core/Security/SecurityModule.php": "50d2760881d6e8bfc89a3f9a9df60405", "modules/Core/Security/Infrastructure/Middleware/UserAuthenticationMiddleware.php": "2d5f213a7288c7ca34a920232ab96edd", "modules/Core/Logging/Infrastructure/Services/LoggerFactory.php": "6be702c93eb9b3d72027faea731a8d6c", "modules/Core/Language/Domain/Contracts/TranslationRepositoryInterface.php": "4584ba9cb294bc6c1e65d5197cda5275", "modules/Core/Language/Domain/Models/Translation.php": "4092592189d2631ed633522ebe2e40c5", "modules/Core/Language/Domain/Services/TranslationService.php": "93a7789fc83d5e1520a21784020c537a", "modules/Core/Language/Domain/Events/TranslationAddedEvent.php": "0c855478fb36ba988a74ac600fd94d27", "modules/Core/Language/Domain/Events/LocaleChangedEvent.php": "9995e57b48817e7cbb01f23ce86234f6", "modules/Core/Language/Domain/ValueObjects/TranslationKey.php": "e263bf019f0b98eba6a299e845c5a3c8", "modules/Core/Language/Domain/ValueObjects/Locale.php": "c5ed518ea11ad3ceffd9d0b040dec4fd", "modules/Core/Language/Actions/Api/LanguageSettingsAction.php": "4f1bffd27d83f6aa582cf47dc72f7a30", "modules/Core/Language/Actions/Api/TranslateAction.php": "a7992c1afaa0a9362e0fea3d0a8a28bb", "modules/Core/Language/Contracts/Services/LocaleServiceInterface.php": "d9178c63312c90e94f7b7c758b748644", "modules/Core/Language/Contracts/Events/LanguageModuleEvents.php": "25530b1f7f3d0899e080627c187e069e", "modules/Core/Language/Application/Actions/Api/TranslateAction.php": "3ca591e3c2a28cb2f66ff7ac151fdce5", "modules/Core/Language/Application/DTOs/TranslateRequest.php": "32ba6308e574175b6fe73d541ba73df5", "modules/Core/Language/Application/DTOs/LanguageSettingsRequest.php": "84ed1003c4ef3317217f877419aaf1ad", "modules/Core/Language/Application/Commands/ChangeLocaleCommand.php": "e0a18a85d85e6efd90ac83a78d525bb8", "modules/Core/Language/Application/Queries/GetTranslationQuery.php": "3cc28505f4991f77f9b08dcc3ad3621e", "modules/Core/Language/Services/LocaleService.php": "0281b990e9abec9b60c6a43898856b2b", "modules/Core/Language/config.php": "5ebe1889bb11b5aafb36ac4dbb7f8733", "modules/Core/Language/Infrastructure/Listeners/LocaleChangedListener.php": "b34227ed72c3f12f89a706423f276eb8", "modules/Core/Language/Infrastructure/Middleware/LocaleMiddleware.php": "8b41a539d5d63ac3e8c0050e902547ca", "modules/Core/ErrorHandling/Infrastructure/Handlers/ErrorResponseHandler.php": "ad28167be6f8304dcae12ca8f91ce00f", "modules/Core/ErrorHandling/Infrastructure/ProblemDetails/ProblemDetails.php": "3fb889857d66246b66eb183ef193d7a1", "modules/Core/ErrorHandling/Infrastructure/Exceptions/AuthorizationException.php": "63eb40013f323ad1af0f9bef612f1637", "modules/Core/ErrorHandling/Infrastructure/Exceptions/ValidationException.php": "86acabb55418adba39fdb90a25b5f7cd", "modules/Core/ErrorHandling/Infrastructure/Exceptions/AuthenticationException.php": "3d3156827c137677e89f27ddc0ff28f3", "modules/Core/ErrorHandling/Infrastructure/Exceptions/ProblemDetailsException.php": "a577a964e84393d17473766ac17fa65b", "modules/Core/ErrorHandling/Infrastructure/Helpers/ErrorHelper.php": "f018fedccf8df93ed188a8cddc2a2459", "modules/Core/ErrorHandling/Infrastructure/Middleware/ErrorHandlerMiddleware.php": "c9e10edcf664ad02329d80b0e085049c", "modules/Core/Documentation/Infrastructure/Actions/DocsViewerAction.php": "2119eda1aba60301c1f814b1b60f03c9", "modules/Core/CQRS/Infrastructure/Bus/CommandBus.php": "3cac40f5b3dfad58bfd5f94b48904949", "modules/Core/CQRS/Infrastructure/Bus/QueryBus.php": "30afa84d200bcc4587c75b1ebad9234f", "modules/Core/CQRS/Infrastructure/Handlers/QueryHandlerInterface.php": "454267ea11bfdad5155f14012b262ac1", "modules/Core/CQRS/Infrastructure/Handlers/CommandHandlerInterface.php": "ab00f445a190aa274cc8b72a1cd658a2", "modules/Core/CQRS/Infrastructure/Events/DomainEventInterface.php": "68bd4d7a6f45f4ae18efae4052b999ee", "modules/Core/CQRS/Infrastructure/Events/EventDispatcher.php": "80e587a071d61b52886c5c50e0821d95", "modules/Core/CQRS/Infrastructure/Commands/CommandInterface.php": "cecf094b82571fc237d80d24c9248a4a", "modules/Core/CQRS/Infrastructure/Queries/QueryInterface.php": "3b571302ab3d3fcb6dab77f51856877d", "modules/Core/Database/Domain/Contracts/DatabaseManagerInterface.php": "ffffcabc71c6f6e64b49276c117a07be", "modules/Core/Database/config.php": "b427bcce122ba935b91dc3e78105b30c", "modules/Core/Database/Infrastructure/Services/DatabaseConnectionManager.php": "df958ace6d03803a6874b76e81d8b273", "modules/Core/Database/Infrastructure/Services/DatabaseManager.php": "8a9c49a4b36e5606e36590ccd5522ffb", "modules/Core/Database/Infrastructure/Factories/RepositoryFactory.php": "96bbd17fbc258aa0efb082412d2c1512", "config/routes/docs.php": "962b7b1650b5605155466cb67fce7652", "config/routes/monitoring.php": "9221b088e7fe7ebbf57e335a10b88a2e", "config/logging/logger.php": "42169c63c3cf1b6b9325afbace80d953", "config/container_old.php": "ce24b8db6df526bb916b83607eaee2f2", "config/language.php": "6a6b43529e2c9849fcb4a0b881e0142e", "config/services/middleware.php": "69b2088ada897c24a715076b38f92c08", "config/services/events.php": "fbb5b7fd39e86ba2ee2230a879f46e74", "config/services/session.php": "dd9473b657d0493995433bd2a5d44e20", "config/services/core_modules.php": "5a1096a494d195d2c5fdeb4a592aa07c", "config/services/database.php": "1462df2a6c9046c1d045c038cdf738d5", "config/services/modules.php": "17846b20dcfe04b0f7510bafac7b18ff", "config/services/template.php": "dccc0fa8dfa21cd9fe46a274802b6c08", "config/services/logging.php": "a9dfb268e83ccf55778d864236b1ba71", "config/services/security.php": "d55ebd9b1539b5329e061ac55744b485", "config/services/user.php": "88657debaa7b240e9945ca725f714a45", "config/services/language.php": "86d05e2393ae1a2dcfc217f1febf466e", "config/services/actions.php": "0bce57fac6fb76bf128d094ae086a34a", "config/services/interfaces.php": "c7de7fe4f66b7645b656615020aecab4", "src/Shared/Helpers/SecurePathHelper.php": "5ba99bdbb980d63dd9735e49454f50eb", "modules/Core/User/routes.php": "f8513d8bd362415a7c453d3d8ea3e92e", "modules/Core/User/Repository/SqliteUserRepository.php": "a03c26826cecf6258d4e3b1a986ab126", "modules/Core/User/Repository/UserRepositoryInterface.php": "3a242fab0d2cbc5ec88daf0278e3192b", "modules/Core/User/Services/UserService.php": "f3f1804d82938f970b90b344421641fb", "modules/Core/User/config.php": "1429b5e8635819151b7da434f8516c7e", "modules/Core/Security/Exception/AuthorizationException.php": "16f850f91295cf293a4afa37485f0cc3", "modules/Core/Security/Exception/AuthenticationException.php": "cea7a4d4cb85315db7f2d23fdbffbfa0", "modules/Core/Security/Exception/SecurityException.php": "5ab6240acb0d79a9d90804a6b2b01869", "modules/Core/Security/routes.php": "b869c76228cd19b3985f9bd04bd6aaab", "modules/Core/Security/Services/AuthenticationService.php": "ccae66b5884921a1c70199389d90b427", "modules/Core/Security/Services/SecurityLoginChecker.php": "57057b5e3b38ed99114ca54829eb595d", "modules/Core/Security/Services/AuthorizationService.php": "396a914e1349b39f64bac0a00b7c4a0a", "modules/Core/Security/Services/JwtService.php": "aec8294ff07ec35b1df45d47daf580fd", "modules/Core/Security/Middleware/AuthorizationMiddleware.php": "11ca19f0ea98df24a4885568c7916626", "bootstrap/ModuleManager.php": "07324ad32e58b81adc55b91accb2e353", "bootstrap/App.php": "f08595f68e141568d27d5d6b0ee9adaa", "config/routes.php": "ce79b7e9e2b105339f19a48e42da5ad1", "config/container.php": "5d66842fff57e0706a730634f9e40899", "config/paths.php": "fac18462883ee90c2cf901e08f0294d2", "public/index.php": "4a5c52b574f571bd1f0a487582c01641"}}