# HDM Boot Architecture

## 🏗 Architektúrna Dokumentácia

### 1. Základná Architektúra [P0]
| Dokument | Popis | Status |
|----------|--------|--------|
| [Clean Architecture](clean-architecture.md) | Implementácia Clean Architecture | ✅ |
| [Domain Driven Design](domain-driven-design.md) | DDD princípy a implementácia | ✅ |
| [Module System](module-isolation.md) | Modulárny systém a izolácia | ✅ |

### 2. Security & Infrastructure [P0]
| Dokument | Popis | Status |
|----------|--------|--------|
| [Security Architecture](security-architecture.md) | Bezpečnostná architektúra | ✅ |
| [Error Handling](error-handling.md) | Systém spracovania chýb | ✅ |
| [Monitoring & Logging](monitoring-logging.md) | Monitoring systém | ✅ |

### 3. Integration & Events [P1]
| Dokument | Popis | Status |
|----------|--------|--------|
| [Event Architecture](event-driven-architecture.md) | Event-driven architektúra | ✅ |
| [Event Examples](event-examples.md) | Príklady použitia eventov | ✅ |
| [Dependency Injection](dependency-injection.md) | DI Container a IoC | ✅ |

### 4. Module Management [P1]
| Dokument | Popis | Status |
|----------|--------|--------|
| [Module Management](module-management.md) | Správa modulov | ✅ |
| [Module Isolation](module-isolation.md) | Izolácia modulov | ✅ |

## 📊 Status Dokumentácie

| Kategória | Hotové | Status |
|-----------|--------|--------|
| Základná Architektúra | 3/3 | ✅ |
| Security & Infrastructure | 3/3 | ✅ |
| Integration & Events | 3/3 | ✅ |
| Module Management | 2/2 | ✅ |

## 🔄 Architektúrne Princípy

### Clean Architecture
- Dependency Rule
- Use Case driven design
- Interface segregation
- Dependency injection

### Domain-Driven Design
- Bounded Contexts
- Aggregate Roots
- Domain Events
- Value Objects

### Event-Driven Architecture
- Event Sourcing
- CQRS pattern
- Message Bus
- Event Store

## 📝 Konvencie

### 1. Vrstvy
```
src/
├── Domain/         # Business Logic
├── Application/    # Use Cases
├── Infrastructure/ # External Concerns
└── Interface/      # UI/API Layer
```

### 2. Moduly
```
Modules/
├── Core/          # Critical Features
├── Optional/      # Additional Features
└── Custom/        # Project Specific
```

### 3. Shared Kernel
```
SharedKernel/
├── Contracts/     # Interfaces
├── Events/        # Domain Events
└── ValueObjects/  # Shared Types
```

## 🎯 Ďalšie kroky

### Priority 0 (Kritické)
- [x] Clean Architecture dokumentácia
- [x] Security Architecture
- [x] Error Handling
- [x] Module System

### Priority 1 (Dôležité)
- [x] Event System
- [x] DI Container
- [x] Module Management
- [ ] Service Layer design

### Priority 2 (Nice to have)
- [ ] Performance optimization
- [ ] Caching strategy
- [ ] Scalability patterns
