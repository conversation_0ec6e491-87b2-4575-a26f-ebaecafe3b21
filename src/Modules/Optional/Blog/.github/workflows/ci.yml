name: Blog Module CI

on:
  push:
    paths:
      - 'src/Modules/Optional/Blog/**'
      - '.github/workflows/blog-module.yml'
  pull_request:
    paths:
      - 'src/Modules/Optional/Blog/**'

jobs:
  test:
    name: Blog Module Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-version: ['8.2', '8.3']
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: pdo, pdo_sqlite, json, mbstring
          coverage: xdebug

      - name: Cache Composer dependencies
        uses: actions/cache@v3
        with:
          path: ~/.composer/cache
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: |
          composer install --no-progress --no-suggest --prefer-dist --optimize-autoloader
          cd src/Modules/Optional/Blog && composer install --no-progress --prefer-dist

      - name: Run Code Style Check
        run: cd src/Modules/Optional/Blog && composer cs:check

      - name: Run Static Analysis
        run: cd src/Modules/Optional/Blog && composer analyse

      - name: Run Tests
        run: cd src/Modules/Optional/Blog && composer test

      - name: Run Tests with Coverage
        run: cd src/Modules/Optional/Blog && composer test:coverage

      - name: Upload Coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./var/coverage/blog-module.xml
          flags: blog-module
          name: blog-module-coverage

  security:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'

      - name: Install dependencies
        run: |
          composer install --no-dev --no-progress --prefer-dist
          cd src/Modules/Optional/Blog && composer install --no-dev --no-progress --prefer-dist

      - name: Run Security Audit
        run: cd src/Modules/Optional/Blog && composer audit

  compatibility:
    name: HDM Boot Compatibility
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'

      - name: Install HDM Boot
        run: composer install --no-progress --prefer-dist

      - name: Test Blog Module Integration
        run: |
          php -r "
          require 'vendor/autoload.php';
          \$container = require 'config/container.php';
          echo 'HDM Boot container loaded successfully!' . PHP_EOL;
          
          // Test Blog module loading
          if (class_exists('HdmBoot\\Modules\\Optional\\Blog\\BlogModule')) {
              echo 'Blog module class found!' . PHP_EOL;
          } else {
              echo 'ERROR: Blog module class not found!' . PHP_EOL;
              exit(1);
          }
          "

      - name: Test Blog Routes
        run: php bin/route-list.php | grep -i blog
