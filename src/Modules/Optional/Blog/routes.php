<?php

declare(strict_types=1);

/**
 * Blog Module Routes.
 *
 * Enhanced routes with API endpoints for Orbit CMS functionality.
 */

use HdmBoot\Modules\Optional\Blog\Application\Actions\BlogHomeAction;
use HdmBoot\Modules\Optional\Blog\Application\Actions\BlogArticleAction;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Slim\App;

return function (App $app): void {
    // Blog web interface routes
    $app->get('/blog', function (ServerRequestInterface $request, ResponseInterface $response) use ($app): ResponseInterface {
        $container = $app->getContainer();
        $action = $container->get(BlogHomeAction::class);
        return $action($request);
    });

    $app->get('/blog/article/{slug}', function (ServerRequestInterface $request, ResponseInterface $response, array $args) use ($app): ResponseInterface {
        $container = $app->getContainer();
        $action = $container->get(BlogArticleAction::class);
        return $action($request);
    });

    $app->get('/blog/categories', function (ServerRequestInterface $request, ResponseInterface $response): ResponseInterface {
        $response->getBody()->write('<h1>Blog Categories - Coming Soon</h1><p><a href="/blog">← Back to Blog</a></p>');
        return $response->withHeader('Content-Type', 'text/html');
    });

    $app->get('/blog/tags', function (ServerRequestInterface $request, ResponseInterface $response): ResponseInterface {
        $response->getBody()->write('<h1>Blog Tags - Coming Soon</h1><p><a href="/blog">← Back to Blog</a></p>');
        return $response->withHeader('Content-Type', 'text/html');
    });

    $app->get('/blog/about', function (ServerRequestInterface $request, ResponseInterface $response): ResponseInterface {
        $response->getBody()->write('<h1>About Blog - Coming Soon</h1><p><a href="/blog">← Back to Blog</a></p>');
        return $response->withHeader('Content-Type', 'text/html');
    });

    // TODO: Blog API routes will be implemented later with proper Actions
};
