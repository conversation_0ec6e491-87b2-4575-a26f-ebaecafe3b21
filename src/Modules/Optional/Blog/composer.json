{"name": "hdm-boot/blog-module", "description": "HDM Boot Blog Module - Standalone blog functionality with Hexagonal Architecture", "type": "hdm-boot-module", "keywords": ["hdm-boot", "blog", "module", "hexagonal-architecture", "ddd"], "license": "MIT", "authors": [{"name": "HDM Boot Team", "email": "<EMAIL>"}], "require": {"php": "^8.2", "responsive-sk/slim4-paths": "^1.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "autoload": {"psr-4": {"HdmBoot\\Modules\\Optional\\Blog\\": "src/"}}, "autoload-dev": {"psr-4": {"HdmBoot\\Modules\\Optional\\Blog\\Tests\\": "tests/"}}, "scripts": {"test": "php run-module-tests.php", "test:coverage": "php run-module-tests.php --coverage", "ci": ["@test"]}, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": {"hdm-boot": {"module-type": "optional", "module-name": "Blog", "module-version": "2.0.0", "dependencies": ["Storage", "Template"], "provides": ["blog-api", "blog-web"], "routes": "routes.php", "config": "config.php", "migrations": "migrations/", "assets": "assets/"}}, "minimum-stability": "stable", "prefer-stable": true}