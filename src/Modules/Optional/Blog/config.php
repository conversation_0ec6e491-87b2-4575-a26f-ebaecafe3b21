<?php

declare(strict_types=1);

/**
 * Blog Module Configuration.
 *
 * Optional module for blog functionality using Core Storage and Template modules.
 */

use HdmBoot\Modules\Optional\Blog\Application\Actions\BlogHomeAction;
use HdmBoot\Modules\Optional\Blog\Application\Actions\BlogArticleAction;

return [
    'name'         => 'Blog',
    'description'  => 'Blog functionality with Orbit-style content management',
    'version'      => '1.0.0',
    'dependencies' => [
        'Core/Storage',
        'Core/Template',
    ],

    'routes' => [
        'GET /blog'                => BlogHomeAction::class,
        'GET /blog/article/{slug}' => BlogArticleAction::class,
        'GET /blog/categories'     => 'Coming Soon - Categories',
        'GET /blog/tags'           => 'Coming Soon - Tags',
        'GET /blog/about'          => 'Coming Soon - About',
    ],

    'services' => [
        // Blog Actions (simplified - no template renderer dependency)
        BlogHomeAction::class => function (\DI\Container $container): BlogHomeAction {
            $responseFactory = $container->get(\Psr\Http\Message\ResponseFactoryInterface::class);
            assert($responseFactory instanceof \Psr\Http\Message\ResponseFactoryInterface);
            return new BlogHomeAction($responseFactory);
        },

        BlogArticleAction::class => function (\DI\Container $container): BlogArticleAction {
            $responseFactory = $container->get(\Psr\Http\Message\ResponseFactoryInterface::class);
            assert($responseFactory instanceof \Psr\Http\Message\ResponseFactoryInterface);
            return new BlogArticleAction($responseFactory);
        },
    ],

    'templates' => [
        'blog' => __DIR__ . '/templates/blog',
    ],

    'settings' => [
        'articles_per_page' => 10,
        'enable_comments'   => false,
        'enable_search'     => true,
        'default_author'    => 'HDM Boot Team',
    ],
];
