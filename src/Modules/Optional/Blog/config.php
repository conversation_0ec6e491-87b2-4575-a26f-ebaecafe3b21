<?php

declare(strict_types=1);

/**
 * Blog Module Configuration.
 *
 * Optional module for blog functionality using Core Storage and Template modules.
 */

use HdmBoot\Modules\Optional\Blog\Application\Actions\BlogHomeAction;
use HdmBoot\Modules\Optional\Blog\Application\Actions\BlogArticleAction;

return [
    'name'         => 'Blog',
    'description'  => 'Blog functionality with Orbit-style content management',
    'version'      => '1.0.0',
    'dependencies' => [
        'Core/Storage',
        'Core/Template',
    ],

    'routes' => [
        'GET /blog'                => BlogHomeAction::class,
        'GET /blog/article/{slug}' => BlogArticleAction::class,
        'GET /blog/categories'     => 'Coming Soon - Categories',
        'GET /blog/tags'           => 'Coming Soon - Tags',
        'GET /blog/about'          => 'Coming Soon - About',
    ],

    'services' => [
        // Blog Actions
        BlogHomeAction::class => function ($container) {
            return new BlogHomeAction(
                $container->get(\HdmBoot\Modules\Core\Template\Domain\Contracts\TemplateRendererInterface::class),
                $container->get(\Psr\Http\Message\ResponseFactoryInterface::class)
            );
        },

        BlogArticleAction::class => function ($container) {
            return new BlogArticleAction(
                $container->get(\HdmBoot\Modules\Core\Template\Domain\Contracts\TemplateRendererInterface::class),
                $container->get(\Psr\Http\Message\ResponseFactoryInterface::class)
            );
        },
    ],

    'templates' => [
        'blog' => __DIR__ . '/templates/blog',
    ],

    'settings' => [
        'articles_per_page' => 10,
        'enable_comments'   => false,
        'enable_search'     => true,
        'default_author'    => 'HDM Boot Team',
    ],
];
