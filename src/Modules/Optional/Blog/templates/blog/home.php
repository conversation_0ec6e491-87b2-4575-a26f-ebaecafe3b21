<?php
// Template variables from BlogHomeAction
// $articles, $allArticles, $categories, $tags, $totalArticles, $publishedCount, $categoriesCount, $tagsCount
// $title, $pageTitle

// Safe defaults
$articlesArray = $articles ?? [];
$allArticlesArray = $allArticles ?? [];
$categoriesArray = $categories ?? [];
$tagsArray = $tags ?? [];
$totalCount = $totalArticles ?? count($allArticlesArray);
$publishedCount = $publishedCount ?? count($articlesArray);
$categoriesCount = $categoriesCount ?? count($categoriesArray);
$tagsCount = $tagsCount ?? count($tagsArray);
$pageTitle = $pageTitle ?? 'Blog Homepage';
$title = $title ?? 'HDM Boot Blog';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title) ?></title>

    <!-- <PERSON><PERSON> Boot Default Theme Assets -->
    <link href="/themes/default/css/styles.min.css" rel="stylesheet">
    <script src="/themes/default/js/app.min.js" defer></script>
</head>
<body class="bg-secondary-50 text-secondary-900">
    <div class="container mx-auto max-w-4xl px-4 py-8">
        <!-- Advanced Navigation Bar -->
        <header class="bg-gradient-primary text-white rounded-lg mb-8 animate-fade-in" x-data="navigation()">
            <!-- Top Bar with Logo and Controls -->
            <div class="flex items-center justify-between p-6 border-b border-white/20">
                <div class="flex items-center gap-4">
                    <h1 class="text-2xl font-bold">🚀 HDM Boot Blog</h1>
                </div>

                <!-- Controls -->
                <div class="flex items-center gap-4">
                    <!-- Search -->
                    <div class="relative" x-data="{ searchOpen: false, ...search() }">
                        <button @click="searchOpen = !searchOpen" class="p-2 hover:bg-white/10 rounded-md transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                        <div x-show="searchOpen" x-transition @click.away="searchOpen = false" class="absolute right-0 top-12 w-96 bg-white dark:bg-secondary-800 rounded-lg shadow-lg p-4 z-50 border border-secondary-200 dark:border-secondary-700">
                            <div class="relative">
                                <input
                                    type="text"
                                    x-model="query"
                                    @input.debounce.300ms="search()"
                                    @keydown.escape="searchOpen = false"
                                    placeholder="Search articles..."
                                    class="w-full px-3 py-2 border border-secondary-300 dark:border-secondary-600 rounded-md text-secondary-900 dark:text-secondary-100 bg-white dark:bg-secondary-700 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                >
                                <div x-show="isSearching" class="absolute right-3 top-3">
                                    <div class="spinner"></div>
                                </div>
                            </div>

                            <!-- Search Results -->
                            <div x-show="results.length > 0" class="mt-4 max-h-64 overflow-y-auto">
                                <div class="text-sm text-secondary-600 dark:text-secondary-400 mb-2">
                                    Found <span x-text="results.length"></span> result(s)
                                </div>
                                <template x-for="result in results" :key="result.url">
                                    <a :href="result.url" class="block p-3 hover:bg-secondary-50 dark:hover:bg-secondary-700 rounded-md border-b border-secondary-100 dark:border-secondary-600 last:border-b-0">
                                        <div class="font-medium text-secondary-900 dark:text-secondary-100" x-text="result.title"></div>
                                        <div class="text-sm text-secondary-600 dark:text-secondary-400 mt-1" x-text="result.excerpt"></div>
                                    </a>
                                </template>
                            </div>

                            <!-- No Results -->
                            <div x-show="query.length > 0 && results.length === 0 && !isSearching" class="mt-4 text-sm text-secondary-600 dark:text-secondary-400 text-center py-4">
                                No articles found for "<span x-text="query"></span>"
                            </div>

                            <!-- Help Text -->
                            <div x-show="query.length === 0" class="mt-2 text-sm text-secondary-600 dark:text-secondary-400">
                                Type to search articles...
                            </div>
                        </div>
                    </div>

                    <!-- Language Switcher -->
                    <div class="relative" x-data="{ langOpen: false }">
                        <button @click="langOpen = !langOpen" class="flex items-center gap-2 p-2 hover:bg-white/10 rounded-md transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                            </svg>
                            <span class="text-sm">EN</span>
                        </button>
                        <div x-show="langOpen" x-transition class="absolute right-0 top-12 bg-white rounded-lg shadow-lg py-2 z-50 min-w-32">
                            <a href="?lang=en" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">🇺🇸 English</a>
                            <a href="?lang=sk" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">🇸🇰 Slovenčina</a>
                            <a href="?lang=cs" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">🇨🇿 Čeština</a>
                        </div>
                    </div>

                    <!-- Dark Mode Toggle -->
                    <button x-data="{ dark: false }" @click="dark = !dark; document.documentElement.classList.toggle('dark')" class="p-2 hover:bg-white/10 rounded-md transition-colors">
                        <svg x-show="!dark" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                        <svg x-show="dark" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </button>

                    <!-- Mobile Menu Toggle -->
                    <button @click="toggle()" class="md:hidden p-2 hover:bg-white/10 rounded-md transition-colors">
                        <svg x-show="!isOpen" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        <svg x-show="isOpen" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Main Navigation -->
            <nav class="p-6" :class="{ 'hidden md:block': !isOpen }">
                <div class="flex flex-col md:flex-row md:items-center md:justify-center gap-4">
                    <a href="/blog" class="nav-link bg-primary-700 hover:bg-primary-600 px-4 py-2 rounded-md transition-colors text-white text-center">
                        <span class="flex items-center justify-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Home
                        </span>
                    </a>
                    <a href="/blog/categories" class="nav-link bg-primary-700 hover:bg-primary-600 px-4 py-2 rounded-md transition-colors text-white text-center">
                        <span class="flex items-center justify-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5a2 2 0 00-2 2v12a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                            </svg>
                            Categories
                        </span>
                    </a>
                    <a href="/blog/tags" class="nav-link bg-primary-700 hover:bg-primary-600 px-4 py-2 rounded-md transition-colors text-white text-center">
                        <span class="flex items-center justify-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                            Tags
                        </span>
                    </a>
                    <a href="/blog/about" class="nav-link bg-primary-700 hover:bg-primary-600 px-4 py-2 rounded-md transition-colors text-white text-center">
                        <span class="flex items-center justify-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            About
                        </span>
                    </a>
                    <a href="/" class="nav-link bg-secondary-600 hover:bg-secondary-700 px-4 py-2 rounded-md transition-colors text-white text-center">
                        <span class="flex items-center justify-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Back to Main
                        </span>
                    </a>
                </div>
            </nav>
        </header>

        <!-- Stats Grid -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8 animate-on-scroll">
            <div class="stat">
                <div class="stat-number"><?= $totalCount; ?></div>
                <div class="stat-label">Total Articles</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?= $publishedCount; ?></div>
                <div class="stat-label">Published</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?= $categoriesCount; ?></div>
                <div class="stat-label">Categories</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?= $tagsCount; ?></div>
                <div class="stat-label">Tags</div>
            </div>
        </div>

        <?php if (empty($articles)) : ?>
            <!-- Empty State -->
            <div class="article animate-fade-in">
                <h2 class="article-title">Welcome to HDM Boot Blog!</h2>
                <p class="text-secondary-600 mb-4">No articles found. Create your first article by adding a Markdown file to the <code class="bg-secondary-100 px-2 py-1 rounded text-sm">content/articles/</code> directory.</p>

                <h3 class="text-lg font-semibold mb-3">Example Article:</h3>
                <pre class="bg-secondary-100 p-4 rounded-lg text-sm overflow-x-auto"><code>---
title: "My First Post"
slug: "my-first-post"
author: "Your Name"
published: true
category: "general"
tags: ["hello", "world"]
---

# My First Post

Hello world! This is my first article.</code></pre>
            </div>
        <?php else : ?>
            <!-- Articles List -->
            <h2 class="text-2xl font-bold mb-6 animate-fade-in">Latest Articles</h2>
    
    <?php
    // Helper function for safe attribute access
    function safeGetAttribute(mixed $obj, string $key, string $default = ''): string
    {
        if (!is_object($obj) || !method_exists($obj, 'getAttribute')) {
            return $default;
        }
        $value = $obj->getAttribute($key);

        return is_string($value) ? $value : $default;
    }

    // Helper function for safe numeric attribute access
    function safeGetNumericAttribute(mixed $obj, string $key, int $default = 0): int
    {
        if (!is_object($obj) || !method_exists($obj, 'getAttribute')) {
            return $default;
        }
        $value = $obj->getAttribute($key);

        return is_numeric($value) ? (int) $value : $default;
    }
    ?>

            <?php
            $articlesIterable = is_iterable($articles) ? $articles : [];
            foreach ($articlesIterable as $index => $article) : ?>
                <article class="article animate-on-scroll">
                    <h2 class="article-title">
                        <a href="/blog/article/<?= urlencode(safeGetAttribute($article, 'slug')); ?>" class="hover:text-primary-600 transition-colors">
                            <?= htmlspecialchars(safeGetAttribute($article, 'title', 'Untitled')); ?>
                        </a>
                    </h2>

                    <div class="article-meta">
                        <span class="text-secondary-600">By <?= htmlspecialchars(safeGetAttribute($article, 'author', 'Unknown')); ?></span>
                        <span class="text-secondary-400">•</span>
                        <span class="text-secondary-600"><?php
                            $publishedAt = safeGetAttribute($article, 'published_at', 'now');
                            $timestamp = strtotime($publishedAt);
                            echo date('F j, Y', $timestamp !== false ? $timestamp : time());
                        ?></span>
                        <span class="text-secondary-400">•</span>
                        <span class="text-secondary-600"><?= safeGetNumericAttribute($article, 'reading_time', 1); ?> min read</span>
                        <?php $category = safeGetAttribute($article, 'category'); ?>
                        <?php if ($category) : ?>
                            <span class="text-secondary-400">•</span>
                            <span class="text-primary-600 font-medium"><?= ucfirst($category); ?></span>
                        <?php endif; ?>
                    </div>

                    <?php $excerpt = safeGetAttribute($article, 'excerpt'); ?>
                    <?php if ($excerpt) : ?>
                        <div class="text-secondary-700 mb-4 leading-relaxed"><?= htmlspecialchars($excerpt); ?></div>
                    <?php endif; ?>

                    <a href="/blog/article/<?= urlencode(safeGetAttribute($article, 'slug')); ?>" class="btn btn-primary inline-flex items-center gap-2 mb-4">
                        Read more
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>

                    <?php
                    $articleTags = is_object($article) && method_exists($article, 'getAttribute')
                    ? $article->getAttribute('tags')
                    : null;
                    if (is_array($articleTags)) : ?>
                        <div class="flex flex-wrap gap-2">
                            <?php foreach ($articleTags as $tag) : ?>
                                <?php if (is_string($tag)) : ?>
                                    <span class="tag"><?= htmlspecialchars($tag); ?></span>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </article>
    <?php endforeach; ?>
        <?php endif; ?>

        <!-- Sidebar -->
        <?php if (!empty($categories) || !empty($tags)) : ?>
            <aside class="bg-white rounded-lg shadow-soft p-6 mt-8 animate-on-scroll">
                <?php if (!empty($categories)) : ?>
                    <h3 class="text-xl font-semibold mb-4">Categories</h3>
                <ul>
                    <?php
                $categoriesIterable = is_iterable($categories) ? $categories : [];
                foreach ($categoriesIterable as $category) : ?>
                            <?php if (is_string($category)) : ?>
                                <?php
                            $categoryCounts ??= [];
                                $categoryData = is_array($categoryCounts) && isset($categoryCounts[$category])
                                ? $categoryCounts[$category]
                                : [];
                                $count = is_array($categoryData) ? count($categoryData) : 0;
                                ?>
                            <li>
                                <a href="/blog/category/<?= urlencode($category); ?>">
                                    <?= ucfirst($category); ?> (<?= $count; ?>)
                                </a>
                            </li>
                            <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
            
            <?php if (!empty($tags)) : ?>
                <h3>Popular Tags</h3>
                <div class="tags">
                    <?php
                    $tagsArray = is_array($tags) ? array_slice($tags, 0, 10) : [];
                foreach ($tagsArray as $tag) : ?>
                            <?php if (is_string($tag)) : ?>
                                <span class="tag"><?= htmlspecialchars($tag); ?></span>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </aside>
        <?php endif; ?>
    </div>
</body>
</html>
