<?php
// Template variables from BlogArticleAction
// $article, $relatedArticles, $title, $pageTitle, $metaDescription

// Type guard for template variables
$article ??= null;
$relatedArticles ??= [];
$title ??= 'HDM Boot Blog';
$pageTitle ??= 'Article';
$metaDescription ??= '';

// Helper function for safe attribute access
function safeGetAttribute(mixed $obj, string $key, string $default = ''): string
{
    if (!is_object($obj) || !method_exists($obj, 'getAttribute')) {
        return $default;
    }
    $value = $obj->getAttribute($key);

    return is_string($value) ? $value : $default;
}

// Helper function for safe numeric attribute access
function safeGetNumericAttribute(mixed $obj, string $key, int $default = 0): int
{
    if (!is_object($obj) || !method_exists($obj, 'getAttribute')) {
        return $default;
    }
    $value = $obj->getAttribute($key);

    return is_numeric($value) ? (int) $value : $default;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars(isset($title) ? (string)$title : 'Article') ?></title>
    <?php if ($metaDescription): ?>
        <meta name="description" content="<?= htmlspecialchars($metaDescription) ?>">
    <?php endif; ?>

    <!-- HDM Boot Default Theme Assets -->
    <link href="/themes/default/css/styles.min.css" rel="stylesheet">
    <script src="/themes/default/js/app.min.js" defer></script>
</head>
<body class="bg-secondary-50 text-secondary-900">
    <div class="container mx-auto max-w-4xl px-4 py-8">
        <!-- Navigation -->
        <nav class="mb-8">
            <a href="/blog" class="btn btn-outline inline-flex items-center gap-2 animate-fade-in">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Blog
            </a>
        </nav>

        <?php if (!$article) : ?>
            <!-- Article Not Found -->
            <div class="article animate-fade-in">
                <h1 class="text-3xl font-bold text-danger-600 mb-4">Article Not Found</h1>
                <p class="text-secondary-600 mb-6">The requested article could not be found.</p>
                <a href="/blog" class="btn btn-primary">← Back to Blog</a>
            </div>
        <?php else : ?>
            <!-- Article Content -->
            <article class="article animate-fade-in">
                <header class="mb-8">
                    <h1 class="text-4xl font-bold text-secondary-900 mb-4"><?= htmlspecialchars(safeGetAttribute($article, 'title', 'Untitled')); ?></h1>

                    <div class="article-meta">
                        <span class="text-secondary-600">By <?= htmlspecialchars(safeGetAttribute($article, 'author', 'Unknown')); ?></span>
                        <span class="text-secondary-400">•</span>
                        <span class="text-secondary-600"><?php
                            $publishedAt = safeGetAttribute($article, 'published_at', 'now');
                            $timestamp = strtotime($publishedAt);
                            echo date('F j, Y', $timestamp !== false ? $timestamp : time());
                        ?></span>
                        <span class="text-secondary-400">•</span>
                        <span class="text-secondary-600"><?= safeGetNumericAttribute($article, 'reading_time', 1); ?> min read</span>
                    </div>
                </header>

                <div class="article-content prose prose-secondary max-w-none mb-8">
                    <?php
                    // Simple markdown to HTML conversion with safe content handling
                    $content = safeGetAttribute($article, 'content', '');

                    $content = preg_replace('/^# (.+)$/m', '<h1>$1</h1>', $content) ?? $content;
                    $content = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $content) ?? $content;
                    $content = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $content) ?? $content;
                    $content = preg_replace('/\*\*(.+?)\*\*/', '<strong>$1</strong>', $content) ?? $content;
                    $content = preg_replace('/\*(.+?)\*/', '<em>$1</em>', $content) ?? $content;
                    $content = preg_replace('/`(.+?)`/', '<code class="bg-secondary-100 px-2 py-1 rounded text-sm">$1</code>', $content) ?? $content;
                    $content = nl2br($content);
                    echo $content;
                    ?>
                </div>

                <?php
                // @phpstan-ignore-next-line booleanAnd.leftAlwaysTrue
                $tags = $article && is_object($article) && method_exists($article, 'getAttribute')
                ? $article->getAttribute('tags')
                : null;
                if (is_array($tags)) : ?>
                    <div class="flex flex-wrap gap-2 mb-8">
                        <?php foreach ($tags as $tag) : ?>
                            <?php if (is_string($tag)) : ?>
                                <span class="tag"><?= htmlspecialchars($tag); ?></span>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </article>

            <!-- Related Articles -->
            <?php if (!empty($relatedArticles)) : ?>
                <section class="mt-12 animate-on-scroll">
                    <h2 class="text-2xl font-bold mb-6">Related Articles</h2>
                    <div class="grid md:grid-cols-3 gap-6">
                        <?php foreach (array_slice(is_array($relatedArticles) ? $relatedArticles : [], 0, 3) as $related) : ?>
                            <article class="card card-body">
                                <h3 class="font-semibold mb-2">
                                    <a href="/blog/article/<?= urlencode(safeGetAttribute($related, 'slug')); ?>" class="hover:text-primary-600 transition-colors">
                                        <?= htmlspecialchars(safeGetAttribute($related, 'title', 'Untitled')); ?>
                                    </a>
                                </h3>
                                <p class="text-sm text-secondary-600">
                                    <?= htmlspecialchars(substr(safeGetAttribute($related, 'excerpt', ''), 0, 100)); ?>...
                                </p>
                            </article>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
