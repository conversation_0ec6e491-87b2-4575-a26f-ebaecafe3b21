<?php

declare(strict_types=1);

namespace HdmBoot\Modules\Optional\Blog\Application\Actions;

use HdmBoot\Modules\Core\Storage\Models\Article;
use HdmBoot\Modules\Core\Storage\Services\FileStorageService;
use HdmBoot\Modules\Core\Template\Domain\Contracts\TemplateRendererInterface;
use HdmBoot\SharedKernel\Services\PathsFactory;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Message\ResponseFactoryInterface;
use ResponsiveSk\Slim4Paths\Paths;

/**
 * Blog Home Action.
 *
 * Handles blog homepage rendering using template system.
 * Part of the Optional Blog module Application layer.
 */
final class BlogHomeAction
{
    public function __construct(
        private readonly TemplateRendererInterface $templateRenderer,
        private readonly ResponseFactoryInterface $responseFactory,
        private readonly ?Paths $paths = null
    ) {
        // Initialize storage service
        $paths = $this->paths ?? PathsFactory::create();
        $storageService = new FileStorageService($paths->content(), $paths);
        Article::setStorageService($storageService);
    }

    public function __invoke(ServerRequestInterface $request): ResponseInterface
    {
        // Get articles data
        $articles = Article::published();
        $allArticles = Article::all();
        $categories = Article::getCategories();
        $tags = Article::getTags();

        // Sort articles by published date (newest first)
        usort($articles, function (Article $a, Article $b): int {
            $aDate = $a->getAttribute('published_at');
            $bDate = $b->getAttribute('published_at');
            $aDateString = is_string($aDate) ? $aDate : '';
            $bDateString = is_string($bDate) ? $bDate : '';

            return strcmp($bDateString, $aDateString);
        });

        // Category counts
        $categoryCounts = [];
        foreach ($categories as $category) {
            $categoryCounts[$category] = Article::byCategory($category);
        }

        // Prepare template data
        $templateData = [
            'articles' => $articles,
            'allArticles' => $allArticles,
            'categories' => $categories,
            'tags' => $tags,
            'categoryCounts' => $categoryCounts,
            'totalArticles' => count($allArticles),
            'publishedCount' => count($articles),
            'categoriesCount' => count($categories),
            'tagsCount' => count($tags),
            'title' => 'HDM Boot Blog',
            'pageTitle' => 'Blog Homepage',
        ];

        // Get template path from Blog module
        $templatePath = __DIR__ . '/../../templates/blog/home.php';
        
        // Render template
        $html = $this->renderTemplate($templatePath, $templateData);

        // Create response
        $response = $this->responseFactory->createResponse();
        $response->getBody()->write($html);
        
        return $response->withHeader('Content-Type', 'text/html');
    }

    /**
     * Render template with data.
     * 
     * Simple PHP template rendering for Blog module.
     */
    private function renderTemplate(string $templatePath, array $data): string
    {
        if (!file_exists($templatePath)) {
            throw new \RuntimeException("Template not found: {$templatePath}");
        }

        // Extract variables for template
        extract($data, EXTR_SKIP);

        // Start output buffering
        ob_start();
        
        try {
            // Include template
            include $templatePath;
            
            // Get rendered content
            $content = ob_get_contents();
            
            if ($content === false) {
                throw new \RuntimeException('Failed to capture template output');
            }
            
            return $content;
        } finally {
            ob_end_clean();
        }
    }
}
