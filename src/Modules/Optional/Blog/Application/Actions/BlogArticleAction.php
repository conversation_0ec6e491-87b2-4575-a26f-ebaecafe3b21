<?php

declare(strict_types=1);

namespace HdmBoot\Modules\Optional\Blog\Application\Actions;

use HdmBoot\Modules\Core\Storage\Models\Article;
use HdmBoot\Modules\Core\Storage\Services\FileStorageService;
use HdmBoot\SharedKernel\Services\PathsFactory;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Message\ResponseFactoryInterface;
use ResponsiveSk\Slim4Paths\Paths;
use Slim\Exception\HttpNotFoundException;

/**
 * Blog Article Action.
 *
 * Handles single article rendering using template system.
 * Part of the Optional Blog module Application layer.
 */
final class BlogArticleAction
{
    public function __construct(
        private readonly ResponseFactoryInterface $responseFactory,
        private readonly ?Paths $paths = null
    ) {
        // Initialize storage service
        $paths = $this->paths ?? PathsFactory::create();
        $storageService = new FileStorageService($paths->content(), $paths);
        Article::setStorageService($storageService);
    }

    public function __invoke(ServerRequestInterface $request): ResponseInterface
    {
        // Get slug from route arguments
        $routeContext = \Slim\Routing\RouteContext::fromRequest($request);
        $route = $routeContext->getRoute();
        
        if ($route === null) {
            throw new HttpNotFoundException($request, 'Route not found');
        }
        
        $slug = $route->getArgument('slug');
        
        if (!is_string($slug) || $slug === '') {
            throw new HttpNotFoundException($request, 'Article slug not provided');
        }

        // Find article
        $article = Article::find($slug);
        
        if ($article === null) {
            throw new HttpNotFoundException($request, "Article not found: {$slug}");
        }

        // Get related articles (same category, excluding current)
        $currentCategory = $article->getAttribute('category');
        $relatedArticles = [];
        
        if (is_string($currentCategory) && $currentCategory !== '') {
            $categoryArticles = Article::byCategory($currentCategory);
            $relatedArticles = array_filter($categoryArticles, function (Article $relatedArticle) use ($slug): bool {
                return $relatedArticle->getAttribute('slug') !== $slug;
            });
            
            // Limit to 3 related articles
            $relatedArticles = array_slice($relatedArticles, 0, 3);
        }

        // Prepare template data
        $templateData = [
            'article' => $article,
            'relatedArticles' => $relatedArticles,
            'title' => $article->getAttribute('title') . ' - HDM Boot Blog',
            'pageTitle' => $article->getAttribute('title'),
            'metaDescription' => $article->getAttribute('excerpt') ?? '',
        ];

        // Get template path from Blog module
        $templatePath = __DIR__ . '/../../templates/blog/article.php';
        
        // Render template
        $html = $this->renderTemplate($templatePath, $templateData);

        // Create response
        $response = $this->responseFactory->createResponse();
        $response->getBody()->write($html);
        
        return $response->withHeader('Content-Type', 'text/html');
    }

    /**
     * Render template with data.
     * 
     * Simple PHP template rendering for Blog module.
     */
    private function renderTemplate(string $templatePath, array $data): string
    {
        if (!file_exists($templatePath)) {
            throw new \RuntimeException("Template not found: {$templatePath}");
        }

        // Extract variables for template
        extract($data, EXTR_SKIP);

        // Start output buffering
        ob_start();
        
        try {
            // Include template
            include $templatePath;
            
            // Get rendered content
            $content = ob_get_contents();
            
            if ($content === false) {
                throw new \RuntimeException('Failed to capture template output');
            }
            
            return $content;
        } finally {
            ob_end_clean();
        }
    }
}
