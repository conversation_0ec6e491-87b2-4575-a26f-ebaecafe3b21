<?php

declare(strict_types=1);

/**
 * HDM Boot Protocol - Database Module Configuration (Hybrid Approach)
 * 
 * Hybrid database architecture:
 * - Primary: Native PDO SQLite managers (V2 scripts)
 */


return [
    'name' => 'Database',
    'version' => '2.3.0',
    'description' => 'HDM Boot Database Module with Native SQLite',
    
    'settings' => [
        'enabled' => true,
        
        // Native PDO SQLite approach (V2 scripts)
        'default_manager' => 'sqlite_pdo',
        
        // Database configuration
        'database_url' => 'sqlite:///var/storage/app.db', // Static path for config
        
        // Supported managers (native PDO SQLite)
        'supported_managers' => [
            'sqlite_pdo' => [
                'description' => 'Native PDO SQLite managers (primary)',
                'managers' => [
                    'mark' => 'MarkSqliteDatabaseManager',
                    'user' => 'UserSqliteDatabaseManager', 
                    'system' => 'SystemSqliteDatabaseManager'
                ],
                'usage' => 'V2 scripts, performance-critical operations'
            ],
            'cakephp' => [
                'usage' => 'Complex queries, schema operations (when needed)'
            ]
        ],
        
        // Validation (active usage)
        'validation' => [
            'enabled' => true,
            'provider' => 'cakephp',
            'usage' => 'AuthenticationValidator, input sanitization'
        ]
    ],
    
    'documentation' => [
        'architecture' => 'docs/DATABASE_ARCHITECTURE.md',
        'v2_scripts' => 'bin/v2/README.md',
        'migration_guide' => 'docs/DATABASE_MIGRATION.md'
    ]

];