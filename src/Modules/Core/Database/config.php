<?php

declare(strict_types=1);

/**
 * HDM Boot Protocol - Database Module Configuration (Hybrid Approach)
 * 
 * Hybrid database architecture:
 * - Primary: Native PDO SQLite managers (V2 scripts)
 * - Strategic: CakePHP Database manager (complex queries)
 * - Active: CakePHP Validation (security)
 */

use HdmBoot\Modules\Core\Database\Infrastructure\Services\CakePHPDatabaseManager;

return [
    'name' => 'Database',
    'version' => '2.3.0',
    'description' => 'HDM Boot Database Module with Hybrid Architecture',
    
    'settings' => [
        'enabled' => true,
        
        // Primary approach: Native PDO SQLite (used by V2 scripts)
        'default_manager' => 'sqlite_pdo',
        
        // Database configuration
        'database_url' => 'sqlite:///' . ($paths instanceof \ResponsiveSk\Slim4Paths\Paths ? $paths->storage('app.db') : 'var/storage/app.db'),
        
        // Supported managers (hybrid approach)
        'supported_managers' => [
            'sqlite_pdo' => [
                'description' => 'Native PDO SQLite managers (primary)',
                'managers' => [
                    'mark' => 'MarkSqliteDatabaseManager',
                    'user' => 'UserSqliteDatabaseManager', 
                    'system' => 'SystemSqliteDatabaseManager'
                ],
                'usage' => 'V2 scripts, performance-critical operations'
            ],
            'cakephp' => [
                'description' => 'CakePHP Database manager (strategic)',
                'manager' => 'CakePHPDatabaseManager',
                'usage' => 'Complex queries, schema operations (when needed)'
            ]
        ],
        
        // Validation (active usage)
        'validation' => [
            'enabled' => true,
            'provider' => 'cakephp',
            'usage' => 'AuthenticationValidator, input sanitization'
        ]
    ],
    
    'services' => [
        // Strategic: CakePHP Database Manager (available but not primary)
        CakePHPDatabaseManager::class => [
            'factory' => function ($container) {
                $paths = $container->get('paths');
                return new CakePHPDatabaseManager($paths);
            },
            'usage' => 'strategic'
        ]
        
        // Note: SQLite managers are created via DatabaseManagerFactory
        // Note: AuthenticationValidator uses CakePHP Validation directly
    ],
    
    'documentation' => [
        'architecture' => 'docs/HYBRID_DATABASE_ARCHITECTURE.md',
        'v2_scripts' => 'bin/v2/README.md',
        'migration_guide' => 'docs/DATABASE_MIGRATION.md'
    ]
];