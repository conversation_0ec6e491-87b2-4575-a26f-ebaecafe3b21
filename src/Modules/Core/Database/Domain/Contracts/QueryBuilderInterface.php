<?php

declare(strict_types=1);

namespace HdmBoot\Modules\Core\Database\Domain\Contracts;

/**
 * Query Builder Interface.
 * 
 * Provides fluent query building capabilities.
 */
interface QueryBuilderInterface
{
    /**
     * Select columns.
     *
     * @param array<string> $columns
     */
    public function select(array $columns = ['*']): self;
    
    /**
     * From table.
     */
    public function from(string $table): self;
    
    /**
     * Where condition.
     */
    public function where(string $column, mixed $operator, mixed $value = null): self;
    
    /**
     * Order by.
     */
    public function orderBy(string $column, string $direction = 'ASC'): self;
    
    /**
     * Limit results.
     */
    public function limit(int $limit): self;
    
    /**
     * Execute query and get results.
     *
     * @return array<int, array<string, mixed>>
     */
    public function get(): array;

    /**
     * Execute query and get first result.
     *
     * @return array<string, mixed>|null
     */
    public function first(): ?array;

    /**
     * Insert data.
     *
     * @param array<string, mixed> $data
     */
    public function insert(array $data): bool;

    /**
     * Update data.
     *
     * @param array<string, mixed> $data
     */
    public function update(array $data): int;
    
    /**
     * Delete records.
     */
    public function delete(): int;
}