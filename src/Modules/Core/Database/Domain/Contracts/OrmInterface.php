<?php

declare(strict_types=1);

namespace HdmBoot\Modules\Core\Database\Domain\Contracts;

/**
 * ORM Interface.
 * 
 * Provides object-relational mapping capabilities.
 */
interface OrmInterface
{
    /**
     * Find entity by ID.
     */
    public function find(mixed $id): ?object;
    
    /**
     * Find all entities.
     */
    public function findAll(): array;
    
    /**
     * Find entities by criteria.
     */
    public function findBy(array $criteria): array;
    
    /**
     * Find one entity by criteria.
     */
    public function findOneBy(array $criteria): ?object;
    
    /**
     * Persist entity.
     */
    public function persist(object $entity): void;
    
    /**
     * Remove entity.
     */
    public function remove(object $entity): void;
    
    /**
     * Flush changes to database.
     */
    public function flush(): void;
    
    /**
     * Clear entity manager.
     */
    public function clear(): void;
}