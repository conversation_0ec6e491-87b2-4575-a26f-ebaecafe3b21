<?php

declare(strict_types=1);

namespace HdmBoot\Modules\Core\Database\Domain\Contracts;

/**
 * Migration Interface.
 * 
 * Provides database migration capabilities.
 */
interface MigrationInterface
{
    /**
     * Run migration up.
     */
    public function up(): void;
    
    /**
     * Run migration down.
     */
    public function down(): void;
    
    /**
     * Get migration version.
     */
    public function getVersion(): string;
    
    /**
     * Get migration description.
     */
    public function getDescription(): string;
    
    /**
     * Check if migration has been applied.
     */
    public function isApplied(): bool;
    
    /**
     * Mark migration as applied.
     */
    public function markAsApplied(): void;
    
    /**
     * Mark migration as reverted.
     */
    public function markAsReverted(): void;
}