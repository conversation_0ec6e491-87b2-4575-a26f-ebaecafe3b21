# HDM Boot Protocol - Root .htaccess
# Performance optimized for boot.responsive.sk

# Enable rewrite engine
RewriteEngine On

# Force HTTPS (optional - uncomment if needed)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE font/woff
    AddOutputFilterByType DEFLATE font/woff2
</IfModule>

# Enable browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # CSS and JavaScript - 1 year
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    
    # Images - 1 year
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    
    # Fonts - 1 year
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML - 1 hour
    ExpiresByType text/html "access plus 1 hour"
    
    # JSON/XML - 5 minutes
    ExpiresByType application/json "access plus 5 minutes"
    ExpiresByType application/xml "access plus 5 minutes"
</IfModule>

# Cache-Control headers
<IfModule mod_headers.c>
    # Remove ETags (we use Last-Modified instead)
    Header unset ETag
    FileETag None
    
    # CSS and JavaScript - immutable cache
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
        Header set Vary "Accept-Encoding"
    </FilesMatch>
    
    # Images - long cache
    <FilesMatch "\.(png|jpg|jpeg|gif|svg|webp|ico)$">
        Header set Cache-Control "public, max-age=31536000"
        Header set Vary "Accept-Encoding"
    </FilesMatch>
    
    # Fonts - long cache
    <FilesMatch "\.(woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000"
        Header set Vary "Accept-Encoding"
    </FilesMatch>
    
    # HTML - short cache
    <FilesMatch "\.html?$">
        Header set Cache-Control "public, max-age=3600"
        Header set Vary "Accept-Encoding"
    </FilesMatch>
    
    # PHP files - no cache for dynamic content
    <FilesMatch "\.php$">
        Header set Cache-Control "no-cache, must-revalidate, max-age=0"
        Header set Pragma "no-cache"
    </FilesMatch>
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # CORS for API (if needed)
    <LocationMatch "^/api/">
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Header set Access-Control-Allow-Headers "Content-Type, Authorization"
        Header set Cache-Control "public, max-age=300"
    </LocationMatch>
</IfModule>

# Security: Block access to sensitive files
<FilesMatch "\.(env|log|sql|md|json|lock|yml|yaml|xml)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to directories
RedirectMatch 404 /\.git
RedirectMatch 404 /vendor
RedirectMatch 404 /var
RedirectMatch 404 /config
RedirectMatch 404 /src

# Performance: Disable server signature
ServerSignature Off

# Performance: Disable access logs for static assets (optional)
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|webp|ico|woff|woff2)$">
    SetEnv dont-log 1
</FilesMatch>

# Main rewrite rules for clean URLs
# Route all requests to public directory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ public/index.php [QSA,L]

# Direct access to public assets
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^(.*)$ public/$1 [QSA,L]

# Handle OPTIONS requests for CORS
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
