/**
 * HDM Boot Protocol Default Theme - Main JavaScript
 * 
 * Features:
 * - AlpineJS for reactive components
 * - GSAP for animations
 * - Theme utilities and helpers
 */

// Import dependencies
import Alpine from 'alpinejs';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Make GSAP available globally
window.gsap = gsap;
window.ScrollTrigger = ScrollTrigger;

// Alpine.js components
document.addEventListener('alpine:init', () => {
  
  // Navigation component
  Alpine.data('navigation', () => ({
    isOpen: false,
    
    toggle() {
      this.isOpen = !this.isOpen;
    },
    
    close() {
      this.isOpen = false;
    },
    
    init() {
      // Close menu when clicking outside
      document.addEventListener('click', (e) => {
        if (!this.$el.contains(e.target)) {
          this.isOpen = false;
        }
      });
    }
  }));
  
  // Alert component
  Alpine.data('alert', (type = 'info', dismissible = true) => ({
    show: true,
    type: type,
    dismissible: dismissible,
    
    dismiss() {
      if (this.dismissible) {
        this.show = false;
      }
    },
    
    init() {
      // Auto-dismiss after 5 seconds for success/info alerts
      if ((this.type === 'success' || this.type === 'info') && this.dismissible) {
        setTimeout(() => {
          this.dismiss();
        }, 5000);
      }
    }
  }));
  
  // Modal component
  Alpine.data('modal', (initialOpen = false) => ({
    open: initialOpen,
    
    show() {
      this.open = true;
      document.body.style.overflow = 'hidden';
    },
    
    hide() {
      this.open = false;
      document.body.style.overflow = '';
    },
    
    init() {
      // Close on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.open) {
          this.hide();
        }
      });
    }
  }));
  
  // Form validation component
  Alpine.data('formValidation', () => ({
    errors: {},
    
    validate(field, value, rules) {
      this.errors[field] = [];
      
      if (rules.required && (!value || value.trim() === '')) {
        this.errors[field].push('This field is required');
      }
      
      if (rules.email && value && !this.isValidEmail(value)) {
        this.errors[field].push('Please enter a valid email address');
      }
      
      if (rules.minLength && value && value.length < rules.minLength) {
        this.errors[field].push(`Minimum length is ${rules.minLength} characters`);
      }
      
      if (rules.maxLength && value && value.length > rules.maxLength) {
        this.errors[field].push(`Maximum length is ${rules.maxLength} characters`);
      }
      
      return this.errors[field].length === 0;
    },
    
    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },
    
    hasError(field) {
      return this.errors[field] && this.errors[field].length > 0;
    },
    
    getError(field) {
      return this.hasError(field) ? this.errors[field][0] : '';
    }
  }));
  
  // Loading state component
  Alpine.data('loading', (initialState = false) => ({
    isLoading: initialState,

    start() {
      this.isLoading = true;
    },

    stop() {
      this.isLoading = false;
    },

    async withLoading(asyncFunction) {
      this.start();
      try {
        await asyncFunction();
      } finally {
        this.stop();
      }
    }
  }));

  // Search component
  Alpine.data('search', () => ({
    query: '',
    results: [],
    isSearching: false,

    async search() {
      if (this.query.length < 2) {
        this.results = [];
        return;
      }

      this.isSearching = true;

      try {
        // Simple client-side search (in real app, this would be API call)
        const articles = document.querySelectorAll('.article');
        this.results = [];

        articles.forEach((article, index) => {
          const title = article.querySelector('.article-title')?.textContent || '';
          const content = article.textContent || '';

          if (title.toLowerCase().includes(this.query.toLowerCase()) ||
              content.toLowerCase().includes(this.query.toLowerCase())) {
            this.results.push({
              title: title,
              url: article.querySelector('a')?.href || '#',
              excerpt: content.substring(0, 150) + '...'
            });
          }
        });
      } catch (error) {
        console.error('Search error:', error);
      } finally {
        this.isSearching = false;
      }
    },

    clearSearch() {
      this.query = '';
      this.results = [];
    }
  }));
  
});

// GSAP Animations
document.addEventListener('DOMContentLoaded', () => {
  
  // Fade in animation for elements with .animate-fade-in
  gsap.from('.animate-fade-in', {
    duration: 0.8,
    opacity: 0,
    y: 30,
    stagger: 0.1,
    ease: 'power2.out',
    clearProps: 'all'
  });
  
  // Scroll-triggered animations
  gsap.utils.toArray('.animate-on-scroll').forEach((element) => {
    gsap.from(element, {
      duration: 0.8,
      opacity: 0,
      y: 50,
      ease: 'power2.out',
      clearProps: 'all',
      scrollTrigger: {
        trigger: element,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse'
      }
    });
  });
  
  // Stats counter animation
  gsap.utils.toArray('.stat-number').forEach((counter) => {
    const target = parseInt(counter.textContent);
    const obj = { value: 0 };
    
    gsap.to(obj, {
      duration: 2,
      value: target,
      ease: 'power2.out',
      onUpdate: () => {
        counter.textContent = Math.round(obj.value);
      },
      scrollTrigger: {
        trigger: counter,
        start: 'top 80%',
        toggleActions: 'play none none none'
      }
    });
  });
  
  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        gsap.to(window, {
          duration: 1,
          scrollTo: target,
          ease: 'power2.inOut'
        });
      }
    });
  });
  
});

// Theme utilities
window.HdmTheme = {
  // Show notification
  notify(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 alert-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    gsap.from(notification, {
      duration: 0.3,
      opacity: 0,
      x: 100,
      ease: 'power2.out'
    });
    
    // Auto remove
    setTimeout(() => {
      gsap.to(notification, {
        duration: 0.3,
        opacity: 0,
        x: 100,
        ease: 'power2.in',
        onComplete: () => {
          notification.remove();
        }
      });
    }, duration);
  },
  
  // Scroll to top
  scrollToTop() {
    gsap.to(window, {
      duration: 1,
      scrollTo: 0,
      ease: 'power2.inOut'
    });
  },
  
  // Theme info
  info: {
    name: __THEME_NAME__,
    version: __THEME_VERSION__,
    publicPath: __PUBLIC_PATH__
  }
};

// Start Alpine.js
Alpine.start();

// Console info
console.log(`🚀 HDM Boot Protocol - ${HdmTheme.info.name} theme v${HdmTheme.info.version} loaded`);
