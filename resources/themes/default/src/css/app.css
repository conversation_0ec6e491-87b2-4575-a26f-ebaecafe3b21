/* HDM Boot Protocol Default Theme - Main CSS */

/* Tailwind CSS imports */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-secondary-900 bg-secondary-50 dark:text-secondary-100 dark:bg-secondary-900;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-secondary-900 dark:text-secondary-100;
  }

  h1 { @apply text-4xl lg:text-5xl; }
  h2 { @apply text-3xl lg:text-4xl; }
  h3 { @apply text-2xl lg:text-3xl; }
  h4 { @apply text-xl lg:text-2xl; }
  h5 { @apply text-lg lg:text-xl; }
  h6 { @apply text-base lg:text-lg; }

  /* Links */
  a {
    @apply text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200;
  }
  
  /* Focus styles */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* Custom components */
@layer components {
  /* Buttons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn border-secondary-300 text-secondary-700 hover:bg-secondary-50 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  /* Cards */
  .card {
    @apply bg-white dark:bg-secondary-800 rounded-lg shadow-soft border border-secondary-200 dark:border-secondary-700;
  }

  .card-header {
    @apply px-6 py-4 border-b border-secondary-200 dark:border-secondary-700;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-secondary-200 dark:border-secondary-700 bg-secondary-50 dark:bg-secondary-900;
  }
  
  /* Forms */
  .form-input {
    @apply block w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-secondary-700 mb-1;
  }
  
  .form-error {
    @apply text-sm text-danger-600 mt-1;
  }
  
  .form-help {
    @apply text-sm text-secondary-500 mt-1;
  }
  
  /* Navigation */
  .nav-link {
    @apply text-secondary-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }
  
  .nav-link.active {
    @apply text-primary-600 bg-primary-50;
  }
  
  /* Alerts */
  .alert {
    @apply p-4 rounded-md border;
  }
  
  .alert-success {
    @apply alert bg-success-50 border-success-200 text-success-800;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 border-warning-200 text-warning-800;
  }
  
  .alert-danger {
    @apply alert bg-danger-50 border-danger-200 text-danger-800;
  }
  
  .alert-info {
    @apply alert bg-primary-50 border-primary-200 text-primary-800;
  }
  
  /* Stats */
  .stat {
    @apply bg-white dark:bg-secondary-800 p-6 rounded-lg shadow-soft border border-secondary-200 dark:border-secondary-700 text-center;
  }

  .stat-number {
    @apply text-3xl font-bold text-primary-600 dark:text-primary-400;
  }

  .stat-label {
    @apply text-sm text-secondary-600 dark:text-secondary-400 mt-1;
  }

  /* Blog specific */
  .article {
    @apply bg-white dark:bg-secondary-800 rounded-lg shadow-soft border border-secondary-200 dark:border-secondary-700 p-6 mb-6;
  }

  .article-title {
    @apply text-2xl font-bold text-secondary-900 dark:text-secondary-100 mb-2;
  }

  .article-meta {
    @apply text-sm text-secondary-500 dark:text-secondary-400 mb-4;
  }

  .article-content {
    @apply prose prose-gray dark:prose-invert max-w-none;
  }

  .tag {
    @apply inline-block bg-secondary-100 dark:bg-secondary-700 text-secondary-700 dark:text-secondary-300 px-2 py-1 rounded text-xs font-medium mr-2 mb-2;
  }
  
  /* Loading states */
  .loading {
    @apply animate-pulse;
  }

  .spinner {
    @apply animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600;
  }

  /* Animation fixes */
  .animate-fade-in,
  .animate-on-scroll {
    opacity: 1 !important; /* Ensure elements are visible after animation */
  }
}

/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  .gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.primary.700'));
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, theme('colors.secondary.600'), theme('colors.secondary.700'));
  }
}
