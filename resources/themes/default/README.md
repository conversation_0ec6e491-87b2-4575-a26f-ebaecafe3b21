# HDM Boot Protocol - Default Theme

Modern, responsive theme for HDM Boot Protocol with TailwindCSS, AlpineJS, and GSAP.

## 🎨 Features

- **TailwindCSS 3.3+** - Utility-first CSS framework
- **AlpineJS 3.13+** - Lightweight reactive framework
- **GSAP 3.12+** - Professional animations
- **Vite 5.0+** - Fast build tool and dev server
- **PostCSS** - CSS processing with autoprefixer
- **Responsive Design** - Mobile-first approach
- **Dark Mode Ready** - Easy to implement
- **Component System** - Reusable UI components

## 🚀 Quick Start

### Development

```bash
# Navigate to theme directory
cd resources/themes/default

# Install dependencies
pnpm install

# Build for production
pnpm run build
```

### Build Output

Built assets are output to `public/themes/default/`:

```
public/themes/default/
├── css/
│   └── styles.min.css
├── js/
│   └── app.min.js
├── images/
├── fonts/
└── manifest.json
```

## 📁 Directory Structure

```
resources/themes/default/
├── package.json          # Dependencies and scripts
├── vite.config.js        # Vite configuration
├── tailwind.config.js    # Tailwind configuration
├── postcss.config.js     # PostCSS configuration
├── src/                  # Source files
│   ├── css/
│   │   └── app.css       # Main CSS with Tailwind
│   ├── js/
│   │   └── app.js        # Main JS with Alpine & GSAP
│   └── assets/           # Images, fonts, etc.
├── views/                # Theme templates
│   ├── layouts/          # Layout templates
│   ├── components/       # Component templates
│   └── pages/            # Page templates
└── dist/                 # Built assets (→ public/themes/default/)
```

## 🎯 Available Scripts

```bash
pnpm run build       # Build for production
pnpm run clean       # Clean node_modules and lock file
pnpm run install-clean # Clean install
pnpm run build-prod  # Clean, install, and build
```

## 🎨 Design System

### Colors

- **Primary**: Blue scale (brand color)
- **Secondary**: Gray scale (text and backgrounds)
- **Success**: Green scale
- **Warning**: Yellow scale
- **Danger**: Red scale

### Components

- **Buttons**: `.btn`, `.btn-primary`, `.btn-secondary`, etc.
- **Cards**: `.card`, `.card-header`, `.card-body`, `.card-footer`
- **Forms**: `.form-input`, `.form-label`, `.form-error`
- **Alerts**: `.alert`, `.alert-success`, `.alert-warning`, etc.
- **Navigation**: `.nav-link`, `.nav-link.active`
- **Stats**: `.stat`, `.stat-number`, `.stat-label`

### Animations

- **Fade In**: `.animate-fade-in`
- **Scroll Triggered**: `.animate-on-scroll`
- **Stats Counter**: `.stat-number` (auto-animated)
- **Custom GSAP**: Available via `window.gsap`

## 🧩 AlpineJS Components

### Navigation
```html
<div x-data="navigation()">
  <button @click="toggle()">Menu</button>
  <nav x-show="isOpen" @click.away="close()">
    <!-- Navigation items -->
  </nav>
</div>
```

### Modal
```html
<div x-data="modal()">
  <button @click="show()">Open Modal</button>
  <div x-show="open" @keydown.escape="hide()">
    <!-- Modal content -->
  </div>
</div>
```

### Form Validation
```html
<div x-data="formValidation()">
  <input 
    @blur="validate('email', $event.target.value, {required: true, email: true})"
    :class="hasError('email') ? 'border-red-500' : ''"
  >
  <span x-show="hasError('email')" x-text="getError('email')"></span>
</div>
```

## 🎬 GSAP Animations

### Basic Usage
```javascript
// Available globally
gsap.from('.element', {duration: 1, opacity: 0, y: 50});

// With ScrollTrigger
gsap.from('.element', {
  duration: 1,
  opacity: 0,
  scrollTrigger: '.element'
});
```

### Theme Utilities
```javascript
// Show notification
HdmTheme.notify('Success!', 'success');

// Scroll to top
HdmTheme.scrollToTop();

// Theme info
console.log(HdmTheme.info);
```

## 🔧 Customization

### Colors
Edit `tailwind.config.js` to customize the color palette:

```javascript
theme: {
  extend: {
    colors: {
      primary: {
        // Your custom primary colors
      }
    }
  }
}
```

### Components
Add custom components in `src/css/app.css`:

```css
@layer components {
  .my-component {
    @apply bg-white p-4 rounded-lg shadow;
  }
}
```

### JavaScript
Add custom functionality in `src/js/app.js` or create separate modules.

## 📱 Responsive Breakpoints

- **sm**: 640px
- **md**: 768px
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px

## 🌙 Dark Mode

Theme is prepared for dark mode. Add dark mode classes:

```html
<div class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
  <!-- Content -->
</div>
```

## 🔗 Integration with HDM Boot

The theme integrates seamlessly with HDM Boot Protocol:

- Uses HDM Boot paths system
- Compatible with template engine
- Follows HDM Boot architecture
- Supports module-specific templates

## 📚 Documentation

- [TailwindCSS Docs](https://tailwindcss.com/docs)
- [AlpineJS Docs](https://alpinejs.dev/)
- [GSAP Docs](https://greensock.com/docs/)
- [Vite Docs](https://vitejs.dev/)

## 🤝 Contributing

1. Follow the existing code style
2. Test your changes
3. Update documentation
4. Submit a pull request

## 📄 License

This theme is part of HDM Boot Protocol and follows the same license.
