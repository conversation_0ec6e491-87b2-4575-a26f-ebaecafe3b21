{"name": "hdm-boot-default-theme", "version": "1.0.0", "description": "HDM Boot Protocol Default Theme with TailwindCSS, AlpineJS, and GSAP", "private": true, "scripts": {"build": "vite build", "clean": "rm -rf node_modules pnpm-lock.yaml", "install-clean": "pnpm run clean && pnpm install", "build-prod": "pnpm run clean && pnpm install && pnpm run build"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8"}, "dependencies": {"alpinejs": "^3.13.3", "gsap": "^3.12.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "keywords": ["hdm-boot", "theme", "tailwindcss", "alpinej<PERSON>", "gsap", "frontend"], "author": "HDM Boot Protocol", "license": "MIT"}