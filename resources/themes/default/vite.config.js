import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  // Build configuration - output to public/themes/default/
  build: {
    outDir: '../../../public/themes/default',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        app: resolve(__dirname, 'src/js/app.js'),
        styles: resolve(__dirname, 'src/css/app.css'),
      },
      output: {
        entryFileNames: 'js/[name].min.js',
        chunkFileNames: 'js/[name].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/\.(css)$/.test(assetInfo.name)) {
            return 'css/[name].min[extname]';
          }
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return 'images/[name][extname]';
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
            return 'fonts/[name][extname]';
          }
          return 'assets/[name][extname]';
        },
      },
    },
    // Minification (default esbuild)
    minify: true,
  },

  // CSS configuration
  css: {
    postcss: './postcss.config.js',
  },

  // Asset handling
  assetsInclude: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg', '**/*.woff', '**/*.woff2'],

  // Define global constants
  define: {
    __THEME_NAME__: JSON.stringify('default'),
    __THEME_VERSION__: JSON.stringify('1.0.0'),
    __PUBLIC_PATH__: JSON.stringify('/themes/default/'),
  },

  // Public base path for assets
  base: '/themes/default/',

  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@css': resolve(__dirname, 'src/css'),
      '@js': resolve(__dirname, 'src/js'),
      '@assets': resolve(__dirname, 'src/assets'),
    },
  },

  // Plugin configuration
  plugins: [
    // Add plugins here if needed
  ],
});
