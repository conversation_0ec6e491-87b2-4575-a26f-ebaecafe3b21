/* Dark mode variables */
:root {
    --bg-primary: #ffffff;
    --text-primary: #333333;
    --bg-secondary: #f8f9fa;
    --border-color: #dee2e6;
    --link-color: #0d6efd;
    --code-bg: #f8f9fa;
    --heading-color: #212529;
    --table-stripe: #f9f9f9;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Dark theme - jemnej<PERSON>ie farby pre oči */
[data-theme="dark"] {
    --bg-primary: #222831;      /* Tmavé pozadie s nádychom modrej */
    --text-primary: #d8d9da;    /* <PERSON><PERSON> intenzívna svetlá */
    --bg-secondary: #393e46;    /* Jemne svetlejšia ako pozadie */
    --border-color: #454e59;    /* Stredne tmavá pre okraje */
    --link-color: #82aae3;      /* <PERSON><PERSON><PERSON><PERSON> modr<PERSON> pre odkazy */
    --code-bg: #2d333b;         /* GitHub dark farba pre kód */
    --heading-color: #e3e3e3;   /* Svetlejšia pre nadpisy */
    --table-stripe: #2b3038;    /* Jemne tmavšia pre pruhované riadky */
    --shadow-color: rgba(0, 0, 0, 0.2);
}

/* Base styles */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.75;         /* Väčší rozostup riadkov */
    font-size: 16px;
    letter-spacing: 0.3px;     /* Lepšia čitateľnosť */
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    color: var(--heading-color);
}

/* Tables */
table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    background-color: var(--bg-primary);
}

tr:nth-child(even) {
    background-color: var(--table-stripe);
}

th, td {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
}

/* Code blocks */
pre, code {
    background-color: var(--code-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.2em 0.4em;
    font-family: 'Fira Code', 'Consolas', monospace;
}

pre {
    padding: 1rem;
    overflow-x: auto;
}

pre code {
    border: none;
    padding: 0;
}

/* Links */
a {
    color: var(--link-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Cards and Containers */
.card, .container {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px var(--shadow-color);
}

/* Theme toggle button */
.theme-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    border-radius: 4px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.theme-toggle:hover {
    background-color: var(--bg-primary);
}

/* Navigation */
nav {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
}

/* Content padding */
.content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Smooth transitions */
* {
    transition: background-color 0.3s ease,
                color 0.3s ease,
                border-color 0.3s ease,
                box-shadow 0.3s ease;
}
