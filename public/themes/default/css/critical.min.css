/* HDM Boot Protocol - Critical CSS */
/* Only essential styles for above-the-fold content */

/* Reset and base */
*,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif}
body{margin:0;line-height:inherit}

/* Dark mode variables */
:root{--primary-600:#2563eb;--primary-700:#1d4ed8;--secondary-50:#f8fafc;--secondary-900:#0f172a}
.dark{--secondary-50:#0f172a;--secondary-900:#f8fafc}

/* Critical layout */
.container{width:100%;margin-left:auto;margin-right:auto;padding-left:1rem;padding-right:1rem}
@media (min-width:640px){.container{max-width:640px}}
@media (min-width:768px){.container{max-width:768px}}
@media (min-width:1024px){.container{max-width:1024px}}
@media (min-width:1280px){.container{max-width:1280px}}

/* Hero section critical styles */
.hero-bg{background:linear-gradient(135deg,#2563eb 0%,#1d4ed8 50%,#1e40af 100%);min-height:100vh;display:flex;align-items:center;justify-content:center;color:white}
.text-6xl{font-size:3.75rem;line-height:1}
.font-black{font-weight:900}
.text-center{text-align:center}
.mb-6{margin-bottom:1.5rem}

/* Navigation critical */
.nav{position:fixed;top:0;width:100%;z-index:50;background:rgba(255,255,255,0.95);backdrop-filter:blur(10px)}
.dark .nav{background:rgba(15,23,42,0.95)}

/* Preloader critical */
.preloader{position:fixed;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,#2563eb,#1d4ed8);display:flex;align-items:center;justify-content:center;z-index:9999}
.preloader.hidden{opacity:0;pointer-events:none;transition:opacity 0.5s ease-out}

/* Utilities */
.hidden{display:none}
.flex{display:flex}
.items-center{align-items:center}
.justify-center{justify-content:center}
.text-white{color:#fff}
.bg-primary-600{background-color:var(--primary-600)}
.hover\:bg-primary-700:hover{background-color:var(--primary-700)}