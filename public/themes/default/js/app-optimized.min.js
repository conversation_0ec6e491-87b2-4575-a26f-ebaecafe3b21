// HDM Boot Protocol - Optimized JavaScript Loader
// Reduces unused JavaScript by lazy loading components

(function() {
    'use strict';
    
    // Critical JavaScript only - load immediately
    const criticalFeatures = {
        // Dark mode toggle (always visible)
        darkMode: function() {
            const toggle = document.querySelector('[data-dark-toggle]');
            if (toggle) {
                toggle.addEventListener('click', function() {
                    document.documentElement.classList.toggle('dark');
                    localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
                });
                
                // Restore dark mode
                if (localStorage.getItem('darkMode') === 'true') {
                    document.documentElement.classList.add('dark');
                }
            }
        },
        
        // Mobile menu toggle
        mobileMenu: function() {
            const toggle = document.querySelector('[data-mobile-toggle]');
            const menu = document.querySelector('[data-mobile-menu]');
            if (toggle && menu) {
                toggle.addEventListener('click', function() {
                    menu.classList.toggle('hidden');
                });
            }
        }
    };
    
    // Lazy load features based on page content
    const lazyFeatures = {
        // Search functionality - only load if search exists
        search: function() {
            return import('/themes/default/js/modules/search.js');
        },
        
        // GSAP animations - only load if animations needed
        animations: function() {
            return import('/themes/default/js/modules/animations.js');
        },
        
        // Stats counters - only load if stats exist
        stats: function() {
            return import('/themes/default/js/modules/stats.js');
        }
    };
    
    // Initialize critical features immediately
    document.addEventListener('DOMContentLoaded', function() {
        Object.values(criticalFeatures).forEach(fn => fn());
        
        // Lazy load features based on page content
        if (document.querySelector('[data-search]')) {
            lazyFeatures.search();
        }
        
        if (document.querySelector('[data-animate]')) {
            lazyFeatures.animations();
        }
        
        if (document.querySelector('[data-stats]')) {
            lazyFeatures.stats();
        }
    });
    
    // Preload critical resources
    const preloadLinks = [
        '/themes/default/css/critical.min.css',
        '/themes/default/js/app-optimized.min.js'
    ];
    
    preloadLinks.forEach(href => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = href;
        link.as = href.endsWith('.css') ? 'style' : 'script';
        document.head.appendChild(link);
    });
})();